.exchange-container {
  display: flex;
  justify-content: center;
  align-items: flex-start;
  margin: 40px 0;
  font-family: 'Vazirma<PERSON>n', <PERSON>homa, Aria<PERSON>, sans-serif;
}
.exchange-box {
  background: #fff;
  border: 3px solid #d32f2f;
  border-radius: 24px;
  padding: 32px 24px;
  width: 400px;
  box-shadow: 0 4px 24px rgba(211,47,47,0.08);
  margin-left: 48px;
  position: relative;
}
.exchange-select {
  width: 100%;
  margin-bottom: 16px;
  border-radius: 12px;
  border: 1px solid #eee;
  padding: 12px 16px;
  font-size: 18px;
  background: #fafafa;
  display: flex;
  align-items: center;
  justify-content: space-between;
}
.exchange-input {
  width: 100%;
  padding: 12px 16px;
  font-size: 20px;
  border-radius: 12px;
  border: 1px solid #eee;
  margin-bottom: 24px;
  background: #fafafa;
  text-align: right;
}
.swap-icon {
  display: flex;
  justify-content: center;
  align-items: center;
  margin: 24px 0;
}
.swap-circle {
  background: #d32f2f;
  color: #fff;
  border-radius: 50%;
  width: 56px;
  height: 56px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 32px;
  border: 4px solid #fff;
  box-shadow: 0 2px 8px rgba(211,47,47,0.12);
}
.exchange-rate {
  color: #888;
  font-size: 15px;
  margin-bottom: 16px;
  text-align: left;
}
.exchange-btn {
  width: 100%;
  background: #d32f2f;
  color: #fff;
  border: none;
  border-radius: 10px;
  padding: 16px 0;
  font-size: 22px;
  font-weight: bold;
  cursor: pointer;
  margin-top: 12px;
  transition: background 0.2s;
}
.exchange-btn:hover {
  background: #b71c1c;
}
.info-box {
  background: #fff;
  border-radius: 0 0 24px 24px;
  box-shadow: 0 4px 24px rgba(211,47,47,0.08);
  padding: 0 0 32px 0;
  width: 340px;
  margin-top: 0;
  position: relative;
}
.info-header {
  background: #d32f2f;
  color: #fff;
  border-radius: 0 0 24px 24px;
  padding: 16px 24px 32px 24px;
  font-size: 20px;
  font-weight: bold;
  position: relative;
  top: -24px;
  margin-bottom: -16px;
}
.info-table {
  background: #fff;
  border-radius: 12px;
  margin: 0 24px;
  box-shadow: 0 2px 8px rgba(211,47,47,0.04);
  margin-top: -24px;
  margin-bottom: 16px;
  padding: 8px 0;
}
.info-table-row {
  display: flex;
  justify-content: space-between;
  padding: 8px 0;
  border-bottom: 1px solid #f0f0f0;
  font-size: 18px;
}
.info-table-row:last-child {
  border-bottom: none;
}
.info-label {
  color: #888;
}
.info-value {
  color: #222;
  font-weight: bold;
}
.exchange-desc {
  margin-top: 48px;
  color: #444;
  font-size: 20px;
  line-height: 1.7;
  text-align: right;
}
.flag {
  width: 28px;
  height: 20px;
  border-radius: 4px;
  margin-left: 8px;
  vertical-align: middle;
}
@media (max-width: 900px) {
  .exchange-container {
    flex-direction: column;
    align-items: center;
  }
  .exchange-box, .info-box {
    margin: 0 0 32px 0;
    width: 95%;
  }
}