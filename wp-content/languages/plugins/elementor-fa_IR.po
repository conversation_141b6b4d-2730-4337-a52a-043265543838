# Translation of Plugins - Elementor Website Builder &#8211; More Than Just a Page Builder - Stable (latest release) in Persian
# This file is distributed under the same license as the Plugins - Elementor Website Builder &#8211; More Than Just a Page Builder - Stable (latest release) package.
msgid ""
msgstr ""
"PO-Revision-Date: 2025-04-15 11:59:35+0000\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Plural-Forms: nplurals=1; plural=0;\n"
"X-Generator: GlotPress/4.0.1\n"
"Language: fa\n"
"Project-Id-Version: Plugins - Elementor Website Builder &#8211; More Than Just a Page Builder - Stable (latest release)\n"

#: modules/atomic-widgets/elements/atomic-paragraph/atomic-paragraph.php:56
msgid "Paragraph"
msgstr "پاراگراف"

#: modules/atomic-widgets/elements/atomic-paragraph/atomic-paragraph.php:44
#: modules/atomic-widgets/elements/atomic-paragraph/atomic-paragraph.php:57
msgid "Type your paragraph here"
msgstr "پاراگراف خود را اینجا تایپ کنید"

#: modules/atomic-widgets/elements/atomic-button/atomic-button.php:59
msgid "Type your button text here"
msgstr "متن دکمه خود را در اینجا تایپ کنید"

#: modules/atomic-widgets/elements/atomic-button/atomic-button.php:58
msgid "Button text"
msgstr "متن دکمه"

#: includes/template-library/sources/cloud.php:108
msgid "New Folder"
msgstr "پوشه جدید"

#: includes/editor-templates/templates.php:125
msgid "Create a New Folder"
msgstr "ایجاد یک پوشه جدید"

#: includes/settings/tools.php:317 modules/admin-bar/module.php:148
msgid "Clear Files & Data"
msgstr "پاک‌سازی فایل‌ها و داده‌ها"

#: includes/controls/gallery.php:126 includes/controls/media.php:322
msgid "This image isn't optimized. You need to connect your Image Optimizer account first."
msgstr "این تصویر بهینه نشده است. ابتدا باید حساب کاربری Image Optimizer خود را متصل کنید."

#: includes/settings/tools.php:314
msgid "Elementor Cache"
msgstr "حافظه پنهان المنتور"

#: includes/editor-templates/templates.php:113
#: includes/editor-templates/templates.php:405
#: includes/template-library/sources/cloud.php:34
#: modules/cloud-library/connect/cloud-library.php:14
#: modules/cloud-library/module.php:38
msgid "Cloud Library"
msgstr "کتابخانه ابری"

#: includes/editor-templates/templates.php:112
#: includes/editor-templates/templates.php:404
msgid "Site Library"
msgstr "کتابخانه سایت"

#: includes/controls/media.php:307
msgid "Image size settings don’t apply to Dynamic Images."
msgstr "تنظیمات اندازه تصویر برای تصاویر پویا اعمال نمی‌شود."

#: modules/ai/site-planner-connect/module.php:51
msgid "Approve & Connect"
msgstr "تایید و اتصال"

#: modules/ai/site-planner-connect/module.php:49
msgid "Connect to Site Planner"
msgstr "اتصال به Site Planner"

#: includes/template-library/sources/local.php:1770
msgid "Sorry, you are not allowed to do that."
msgstr "متاسفیم، شما اجازه این کار را ندارید."

#: modules/nested-tabs/widgets/nested-tabs.php:182
msgid "Add Tab"
msgstr "افزودن زبانه"

#: modules/element-cache/module.php:127
msgid "Element Cache"
msgstr "حافظه پنهان عنصر"

#: modules/ai/module.php:222 modules/ai/module.php:257
msgid "Animate With AI"
msgstr "متحرک‌سازی با هوش مصنوعی"

#: modules/atomic-widgets/elements/div-block/div-block.php:28
msgid "Div Block"
msgstr "بلوک Div"

#: includes/widgets/video.php:416
msgid "Captions"
msgstr "زیرنویس‌ها"

#: includes/managers/elements.php:298
msgid "Hello+"
msgstr "Hello+"

#: includes/widgets/image-gallery.php:303
msgid "Custom Gap"
msgstr "شکاف سفارشی"

#: core/experiments/manager.php:378
msgid "Load Google Fonts locally"
msgstr "بارگذاری فونت‌های گوگل به صورت محلی"

#: modules/promotions/promotion-data.php:110
msgid "Upgrade Your Testimonials"
msgstr "توصیه‌نامه‌های خود را ارتقا دهید"

#: core/admin/admin-notices.php:420
msgid "Use Site Mailer for improved email deliverability, detailed email logs, and an easy setup."
msgstr "برای بهبود تحویل ایمیل، گزارش‌های دقیق ایمیل و تنظیم آسان از Site Mailer استفاده کنید."

#: core/admin/admin-notices.php:419
msgid "Ensure your form emails avoid the spam folder!"
msgstr "مطمئن شوید که ایمیل‌های فرم شما به پوشه اسپم نمی‌روند!"

#: modules/checklist/steps/setup-header.php:70
msgid "Add a header"
msgstr "افزودن یک سربرگ"

#: modules/checklist/steps/setup-header.php:62
msgid "Set up a header"
msgstr "تنظیم یک سربرگ"

#: modules/checklist/steps/set-fonts-and-colors.php:31
msgid "Global colors and fonts ensure a cohesive look across your site. Start by defining one color and one font."
msgstr "رنگ‌ها و فونت‌های سراسری یک ظاهر منسجم را در سراسر سایت شما تضمین می‌کنند. با تعریف یک رنگ و یک فونت شروع کنید."

#: modules/checklist/steps/set-fonts-and-colors.php:27
msgid "Set up your Global Fonts & Colors"
msgstr "فونت‌ها و رنگ‌های سراسری خود را تنظیم کنید"

#: modules/checklist/steps/assign-homepage.php:31
msgid "Assign homepage"
msgstr "تعیین صفحه اصلی"

#: modules/checklist/steps/assign-homepage.php:27
msgid "Before your launch, make sure to assign a homepage so visitors have a clear entry point into your site."
msgstr "قبل از راه اندازی، مطمئن شوید که یک صفحه اصلی تعیین کرده‌اید تا بازدیدکنندگان یک نقطه ورود واضح به سایت شما داشته باشند."

#: modules/checklist/steps/assign-homepage.php:23
msgid "Assign a homepage"
msgstr "تعیین یک صفحه اصلی"

#: modules/checklist/steps/add-logo.php:33
#: modules/checklist/steps/set-fonts-and-colors.php:35
msgid "Go to Site Identity"
msgstr "به هویت سایت بروید"

#: modules/checklist/steps/add-logo.php:29
msgid "Let's start by adding your logo and filling in the site identity settings. This will establish your initial presence and also improve SEO."
msgstr "بیایید با اضافه کردن لوگوی خود و پر کردن تنظیمات هویت سایت شروع کنیم. این حضور اولیه، شما را تثبیت می‌کند و همچنین سئو را بهبود می‌بخشد."

#: modules/checklist/steps/add-logo.php:25
msgid "Add your logo"
msgstr "لوگوی خود را اضافه کنید"

#: core/settings/editor-preferences/model.php:187
msgid "Show a checklist to guide you through your first steps of website creation."
msgstr "نمایش یک چک لیست که شما را در اولین مراحل ایجاد وب‌سایت راهنمایی کند."

#: core/experiments/manager.php:344
msgid "Create advanced layouts and responsive designs with %1$sFlexbox%2$s and %3$sGrid%4$s container elements. Give it a try using the %5$sContainer playground%6$s."
msgstr "با عناصر کانتینر %1$sفلکس باکس%2$s و %3$sشبکه%4$s، طرح‌بندی‌های پیشرفته و طرح‌های واکنشگرا ایجاد کنید. با استفاده از %5$sزمین بازی کانتینر%6$s آن را امتحان کنید."

#: modules/checklist/steps/setup-header.php:66
msgid "This element applies across different pages, so visitors can easily navigate around your site."
msgstr "این عنصر در صفحات مختلف اعمال می‌شود تا بازدیدکنندگان به راحتی در سایت شما جابجا شوند."

#: modules/floating-buttons/widgets/floating-bars-var-1.php:25
msgid "Floating Bar CTA"
msgstr "نوار CTA شناور"

#: modules/floating-buttons/module.php:46 assets/js/editor.js:50861
msgid "Floating Bars"
msgstr "نوارهای شناور"

#: modules/floating-buttons/documents/floating-buttons.php:203
#: modules/floating-buttons/module.php:373
msgid "Floating Element"
msgstr "عنصر شناور"

#: modules/floating-buttons/base/widget-floating-bars-base.php:1259
msgid "Headline"
msgstr "عنوان"

#: modules/floating-buttons/base/widget-floating-bars-base.php:1213
msgid "Element spacing"
msgstr "فاصله عنصر"

#: modules/floating-buttons/base/widget-floating-bars-base.php:1049
msgid "Horizontal position"
msgstr "‫موقعیت افقی"

#: modules/floating-buttons/base/widget-floating-bars-base.php:339
msgid "Enter your text"
msgstr "متن خود را وارد کنید"

#: modules/floating-buttons/base/widget-floating-bars-base.php:315
msgid "Headlines"
msgstr "عناوین"

#: modules/floating-buttons/base/widget-floating-bars-base.php:240
msgid "Pause Icon"
msgstr "آیکون مکث"

#: modules/floating-buttons/base/widget-floating-bars-base.php:228
#: modules/floating-buttons/base/widget-floating-bars-base.php:1130
msgid "Pause and Play"
msgstr "مکث و اجرا"

#: modules/floating-buttons/base/widget-floating-bars-base.php:219
#: modules/floating-buttons/base/widget-floating-bars-base.php:1173
msgid "Floating Bar"
msgstr "نوار شناور"

#: modules/floating-buttons/base/widget-floating-bars-base.php:157
msgid "Shop now"
msgstr "اکنون خرید کنید"

#: modules/floating-buttons/base/widget-floating-bars-base.php:156
#: modules/floating-buttons/base/widget-floating-bars-base.php:204
msgid "Enter text"
msgstr "درج متن"

#: modules/floating-buttons/base/widget-floating-bars-base.php:143
#: modules/floating-buttons/base/widget-floating-bars-base.php:555
msgid "CTA Button"
msgstr "دکمه CTA"

#: modules/floating-buttons/base/widget-floating-bars-base.php:131
msgid "Enter your text here"
msgstr "متن خود را اینجا وارد کنید"

#: modules/floating-buttons/base/widget-floating-bars-base.php:105
#: modules/floating-buttons/base/widget-floating-bars-base.php:391
msgid "Announcement"
msgstr "اعلامیه"

#: modules/floating-buttons/base/widget-floating-bars-base.php:69
msgid "Banner"
msgstr "بنر"

#: modules/floating-buttons/base/widget-floating-bars-base.php:64
msgid "Just in! Cool summer tees"
msgstr "همین الان! تیشرت‌های تابستانی خنک"

#: modules/floating-buttons/base/widget-contact-button-base.php:764
msgid "Add up to <b>%d</b> contact buttons"
msgstr "تا <b>%d</b> دکمه تماس اضافه کنید"

#: modules/floating-buttons/base/widget-contact-button-base.php:751
msgid "Add between <b>%1$d</b> to <b>%2$d</b> contact buttons"
msgstr "بین <b>%1$d</b> تا <b>%2$d</b> دکمه تماس اضافه کنید"

#: modules/floating-buttons/admin-menu-items/floating-buttons-menu-item.php:22
#: modules/floating-buttons/admin-menu-items/floating-buttons-menu-item.php:26
#: modules/floating-buttons/documents/floating-buttons.php:207
msgid "Floating Elements"
msgstr "عناصر شناور"

#: modules/checklist/steps/create-pages.php:36
msgid "Create a new page"
msgstr "یک برگه تازه ایجاد کنید"

#: modules/checklist/steps/create-pages.php:28
msgid "Create your first 3 pages"
msgstr "۳ برگه اول خودتان را ایجاد کنید"

#: modules/atomic-widgets/elements/atomic-heading/atomic-heading.php:48
msgid "Your Title Here"
msgstr "عنوان شما اینجاست"

#: modules/atomic-widgets/elements/atomic-heading/atomic-heading.php:63
msgid "Tag"
msgstr "برچسب"

#: modules/ai/preferences.php:61
msgid "Elementor - AI"
msgstr "المنتور - هوش مصنوعی"

#: includes/admin-templates/new-floating-elements.php:50
msgid "Create Floating Element"
msgstr "ایجاد عنصر شناور"

#: includes/admin-templates/new-floating-elements.php:31
msgid "Choose Floating Element"
msgstr "انتخاب عنصر شناور"

#: includes/admin-templates/new-floating-elements.php:21
msgid "Use floating elements to engage your visitors and increase conversions."
msgstr "از عناصر شناور برای جذب بازدیدکنندگان و افزایش تبدیل استفاده کنید."

#. translators: %1$s Span open tag, %2$s: Span close tag.
#: includes/admin-templates/new-floating-elements.php:16
msgid "Floating Elements Help You %1$sWork Efficiently%2$s"
msgstr "عناصر شناور به شما کمک می‌کنند تا %2$sکارآمدتر%1$s کار کنید"

#: core/debug/classes/shop-page-edit.php:23
msgid "Sorry, The content area was not been found on your page"
msgstr "با عرض پوزش، ناحیه محتوا در صفحه شما یافت نشد"

#: modules/atomic-widgets/elements/atomic-image/atomic-image.php:26
msgid "Atomic Image"
msgstr "تصویر اتمی"

#: modules/atomic-widgets/elements/atomic-heading/atomic-heading.php:31
msgid "Atomic Heading"
msgstr "تیتر اتمی"

#: modules/atomic-widgets/module.php:110
msgid "Enable atomic widgets."
msgstr "فعال‌سازی ابزارک‌های اتمی."

#: modules/atomic-widgets/module.php:109
msgid "Atomic Widgets"
msgstr "ابزارک‌های اتمی"

#: modules/ai/preferences.php:73
msgid "Enable Elementor AI functionality"
msgstr "فعال‌سازی قابلیت هوش مصنوعی المنتور"

#: core/settings/editor-preferences/model.php:182
#: modules/checklist/module.php:224
msgid "Launchpad Checklist"
msgstr "چک لیست Launchpad"

#: modules/floating-buttons/base/widget-floating-bars-base.php:199
msgid "Accessible Name"
msgstr "نام قابل دسترسی"

#: modules/floating-buttons/base/widget-contact-button-base.php:463
#: modules/floating-buttons/base/widget-contact-button-base.php:719
msgid "Accessible name"
msgstr "نام قابل دسترس"

#. translators: 1: Platform name.
#: modules/floating-buttons/classes/render/contact-buttons-render-base.php:270
msgid "Open %1$s"
msgstr "باز کردن %1$s"

#. translators: 1: Accessible name.
#: modules/floating-buttons/classes/render/contact-buttons-render-base.php:112
#: modules/floating-buttons/classes/render/floating-bars-core-render.php:112
msgid "Close %1$s"
msgstr "بستن %1$s"

#. translators: 1: Accessible name.
#: modules/floating-buttons/classes/render/contact-buttons-render-base.php:87
msgid "Toggle %1$s"
msgstr "تغییر %1$s"

#: modules/floating-buttons/base/widget-contact-button-base.php:466
#: modules/floating-buttons/base/widget-contact-button-base.php:722
msgid "Add accessible name"
msgstr "افزودن نام قابل دسترس"

#: includes/settings/settings.php:466
msgid "Improve initial page load performance by lazy loading all background images except the first one."
msgstr "با بارگذاری تنبل همه تصاویر پس‌زمینه به جز تصویر اول، عملکرد بارگذاری اولیه صفحه را بهبود دهید."

#: includes/settings/settings.php:336
msgid "Personalize the way Elementor works on your website by choosing the advanced features and how they operate."
msgstr "با انتخاب ویژگی‌های پیشرفته و نحوه عملکرد آنها، نحوه کار المنتور را در وب‌سایت خود را شخصی‌سازی کنید."

#: core/debug/classes/shop-page-edit.php:15
msgid "You are trying to edit the Shop Page although it is a Product Archive. Use the Theme Builder to create your Shop Archive template instead"
msgstr "شما در حال تلاش برای ویرایش صفحه فروشگاه هستید، در حالی که این صفحه یک بایگانی محصولات است. از پوسته‌ساز برای ایجاد قالب بایگانی فروشگاه خود استفاده کنید."

#: includes/settings/settings.php:259
msgid "Tailor how Elementor enhances your site, from post types to other functions."
msgstr "سفارشی کنید که چگونه المنتور سایت شما را بهبود می‌بخشد، از نوع پست‌ها گرفته تا سایر عملکردها."

#: modules/checklist/module.php:225
msgid "Launchpad Checklist feature to boost productivity and deliver your site faster"
msgstr "ویژگی چک لیست Launchpad برای افزایش بهره‌وری و ارائه سریعتر سایت شما"

#: modules/checklist/steps/create-pages.php:32
msgid "Jumpstart your creation with professional designs from the Template Library or start from scratch."
msgstr "ایجاد کردن را با طرح‌های حرفه‌ای از کتابخانه قالب‌ها آغاز کنید یا از ابتدا شروع کنید."

#: modules/floating-buttons/base/widget-floating-bars-base.php:1183
msgid "Align Elements"
msgstr "تراز عناصر"

#: modules/link-in-bio/widgets/link-in-bio.php:28
msgid "Minimalist"
msgstr "مینیمالیست"

#: modules/link-in-bio/base/widget-link-in-bio-base.php:1542
msgid "Dimensions"
msgstr "ابعاد"

#: modules/link-in-bio/base/widget-link-in-bio-base.php:332
msgid "CTA link"
msgstr "لینک CTA"

#: modules/link-in-bio/base/widget-link-in-bio-base.php:284
#: assets/js/packages/editor-controls/editor-controls.js:8
msgid "Add item"
msgstr "افزودن مورد"

#: modules/link-in-bio/base/widget-link-in-bio-base.php:242
msgid "Images Per Row"
msgstr "تصاویر در هر ردیف"

#: modules/link-in-bio/base/widget-link-in-bio-base.php:227
msgid "Add up to <b>%d</b> Images"
msgstr "تا <b>%d</b> تصویر اضافه کنید"

#: modules/link-in-bio/base/widget-link-in-bio-base.php:215
#: modules/link-in-bio/base/widget-link-in-bio-base.php:1023
msgid "Image Links"
msgstr "لینک‌های تصویر"

#: modules/floating-buttons/widgets/contact-buttons.php:26
msgid "Single Chat"
msgstr "گفتگوی تکی"

#: modules/floating-buttons/module.php:375
msgid "Add a Floating element so your users can easily get in touch!"
msgstr "یک عنصر شناور اضافه کنید تا کاربران شما بتوانند به راحتی در تماس باشند!"

#: modules/floating-buttons/module.php:308
msgid "Entire Site"
msgstr "کل سایت"

#: modules/floating-buttons/module.php:222
msgid "Instances"
msgstr "موارد"

#: modules/floating-buttons/module.php:219
msgid "Click Tracking"
msgstr "ردیابی کلیک"

#: modules/floating-buttons/documents/floating-buttons.php:195
msgid "Set as Entire Site"
msgstr "تنظیم برای کل سایت"

#: modules/floating-buttons/documents/floating-buttons.php:188
msgid "Remove From Entire Site"
msgstr "حذف از کل سایت"

#: modules/floating-buttons/documents/floating-buttons.php:32
msgid "After publishing this widget, you will be able to set it as visible on the entire site in the Admin Table."
msgstr "پس از انتشار این ابزارک، می‌توانید آن را در جدول مدیر به عنوان قابل مشاهده در کل سایت تنظیم کنید."

#: modules/announcements/module.php:109
msgid "<p>With AI for text, code, image generation and editing, you can bring your vision to life faster than ever. Start your free trial now - <b>no credit card required!</b></p>"
msgstr "<p>با استفاده از هوش مصنوعی برای تولید و ویرایش متن، کد و تصویر، می‌توانید تصور خود را سریع‌تر از همیشه به واقعیت تبدیل کنید. هم‌اکنون دوره آزمایشی رایگان خود را آغاز کنید - <b>بدون نیاز به کارت اعتباری!</b></p>"

#: modules/announcements/module.php:108
msgid "Discover your new superpowers "
msgstr "اَبَرقدرت‌های جدید خود را کشف کنید "

#: modules/floating-buttons/classes/render/contact-buttons-core-render.php:58
msgid "Links window"
msgstr "پنجره لینک‌ها"

#: modules/floating-buttons/base/widget-contact-button-base.php:3080
#: modules/floating-buttons/base/widget-floating-bars-base.php:1488
msgid "CSS"
msgstr "CSS"

#: modules/floating-buttons/base/widget-contact-button-base.php:3043
msgid "Full Width on Mobile"
msgstr "تمام عرض در موبایل"

#: modules/floating-buttons/base/widget-contact-button-base.php:2479
#: modules/floating-buttons/base/widget-contact-button-base.php:2499
msgid "Text and Icon Color"
msgstr "رنگ متن و آیکون"

#: modules/floating-buttons/base/widget-contact-button-base.php:2438
msgid "Link Spacing"
msgstr "فاصله لینک"

#: modules/floating-buttons/base/widget-contact-button-base.php:2388
msgid "Info Links"
msgstr "لینک‌های اطلاعات"

#: modules/floating-buttons/base/widget-contact-button-base.php:2254
msgid "Resource Links"
msgstr "لینک‌های منبع"

#: modules/floating-buttons/base/widget-contact-button-base.php:2150
msgid "Button Bar"
msgstr "نوار دکمه"

#: modules/floating-buttons/base/widget-contact-button-base.php:2054
msgid "Buttons Spacing"
msgstr "فاصله دکمه‌ها"

#: modules/floating-buttons/base/widget-contact-button-base.php:1828
msgid "Bubble Background Color"
msgstr "رنگ پس‌زمینه حباب"

#: modules/floating-buttons/base/widget-contact-button-base.php:1485
msgid "Hover animation is <b>desktop only</b>"
msgstr "انیمیشن هاور <b>فقط برای دسکتاپ</b> است"

#: modules/floating-buttons/base/widget-contact-button-base.php:826
msgid "Enter description"
msgstr "توضیح وارد کنید"

#: modules/floating-buttons/base/widget-contact-button-base.php:811
msgid "Enter title"
msgstr "عنوان را وارد کنید"

#: modules/floating-buttons/base/widget-contact-button-base.php:736
msgid "Start conversation:"
msgstr "شروع گفتگو:"

#: modules/floating-buttons/base/widget-contact-button-base.php:691
msgid "Typing Animation"
msgstr "انیمیشن تایپ کردن"

#: modules/floating-buttons/base/widget-contact-button-base.php:626
msgid "Active Dot"
msgstr "نقطه فعال"

#: modules/floating-buttons/base/widget-contact-button-base.php:552
msgid "Enter the text"
msgstr "متن را وارد کنید"

#: modules/floating-buttons/base/widget-contact-button-base.php:546
#: modules/floating-buttons/base/widget-contact-button-base.php:734
#: modules/floating-buttons/base/widget-contact-button-base.php:1874
msgid "Call to Action Text"
msgstr "متن دعوت به اقدام (CTA)"

#: modules/floating-buttons/base/widget-contact-button-base.php:537
msgid "Call to Action"
msgstr "دعوت به اقدام (CTA)"

#: modules/floating-buttons/base/widget-contact-button-base.php:536
msgid "Contact Details"
msgstr "جزئیات تماس"

#: modules/floating-buttons/base/widget-contact-button-base.php:532
msgid "Display Text"
msgstr "نمایش متن"

#: modules/floating-buttons/base/widget-contact-button-base.php:516
msgid "Notification Dot"
msgstr "نقطه اعلان"

#: modules/floating-buttons/base/widget-contact-button-base.php:405
#: modules/floating-buttons/base/widget-contact-button-base.php:965
#: modules/link-in-bio/base/widget-link-in-bio-base.php:514
#: modules/link-in-bio/base/widget-link-in-bio-base.php:759
msgid "Paste Waze link"
msgstr "چسباندن لینک Waze"

#: modules/floating-buttons/base/widget-contact-button-base.php:86
msgid "Call now"
msgstr "اکنون تماس بگیرید"

#: modules/floating-buttons/module.php:45 assets/js/editor.js:50863
#: assets/js/editor.js:50865
msgid "Floating Buttons"
msgstr "دکمه‌های شناور"

#: modules/editor-events/module.php:47
msgid "Elementor Editor Events"
msgstr "رویدادهای ویرایشگر المنتور"

#. translators: 1: `<a>` opening tag, 2: `</a>` closing tag.
#: includes/widgets/video.php:342
msgid "Note: Autoplay is affected by %1$s Google’s Autoplay policy %2$s on Chrome browsers."
msgstr "توجه: پخش خودکار در مرورگرهای کروم، تحت تأثیر %1$s خط‌مشی پخش خودکار گوگل %2$s است."

#: includes/widgets/image.php:386
msgid "Scale Down"
msgstr "کاهش ابعاد"

#. translators: %s: <head> tag.
#: includes/settings/settings.php:423
msgid "Internal Embedding places all CSS in the %s which works great for troubleshooting, while External File uses external CSS file for better performance (recommended)."
msgstr "جایگذاری درونی همه CSS ها را در %s قرار می‌دهد که برای عیب‌یابی عالی عمل می‌کند، در حالی که فایل خارجی، از فایل CSS خارجی برای عملکرد بهتر استفاده می‌کند (توصیه می‌شود)."

#: includes/editor-templates/panel.php:142
msgid "Copy and Share Link"
msgstr "کپی و به اشتراک گذاری لینک"

#: core/document-types/page-base.php:188
msgid "No %s found."
msgstr "%s یافت نشد."

#: core/document-types/page-base.php:187
msgid "Search %s"
msgstr "جستجوی %s"

#: core/document-types/page-base.php:185
msgid "New %s"
msgstr "%s تازه"

#: modules/editor-events/module.php:48
msgid "Editor events processing"
msgstr "پردازش رویدادهای ویرایشگر"

#: core/document-types/page-base.php:181
msgid "All %s"
msgstr "همه %s"

#: core/document-types/page-base.php:189
msgid "No %s found in Trash."
msgstr "هیچ %s در زباله‌دان یافت نشد."

#: modules/floating-buttons/base/widget-contact-button-base.php:2224
msgid "Adjust transition duration to change the speed of the <b>hover animation on desktop</b> and the <b>click animation on touchscreen</b>."
msgstr "مدت زمان تحول را تنظیم کنید تا سرعت <b>انیمیشن شناور در دسکتاپ</b> و <b>انیمیشن کلیک در صفحه‌نمایش لمسی</b> را تغییر دهید."

#: modules/floating-buttons/base/widget-contact-button-base.php:2105
msgid "Tooltips"
msgstr "نکات راهنما"

#: modules/floating-buttons/base/widget-contact-button-base.php:146
msgid "Tooltip"
msgstr "نکته راهنما"

#: core/kits/documents/tabs/settings-background.php:75
msgid "Overscroll Behavior"
msgstr "رفتار اسکرول بیش از حد"

#: modules/floating-buttons/base/widget-contact-button-base.php:3067
#: modules/floating-buttons/base/widget-floating-bars-base.php:1475
msgid "Responsive visibility will take effect only on preview mode or live page, and not while editing in Elementor."
msgstr "قابلیت مشاهده واکنش‌گرا فقط در حالت پیش‌نمایش یا صفحه زنده قابل مشاهده است و نه در هنگام ویرایش در المنتور."

#: includes/controls/repeater.php:194
msgid "In a Repeater control, if you specify a minimum number of items, you must also specify a default value that contains at least that number of items."
msgstr "در یک کنترل تکرار کننده، اگر حداقل تعداد آیتم‌ها را مشخص کنید، باید یک مقدار پیش‌فرض تعیین کنید که حداقل شامل همان تعداد آیتم‌ها باشد."

#: modules/element-cache/module.php:144
msgid "Specify the duration for which data is stored in the cache. Elements caching speeds up loading by serving pre-rendered copies of elements, rather than rendering them fresh each time. This control ensures efficient performance and up-to-date content."
msgstr "مدت زمان ذخیره داده‌ها در حافظه پنهان را مشخص کنید. ذخیره عناصر با ارائه کپی‌های از پیش رندر شده عناصر به جای اینکه هر بار آنها را تازه به نمایش بگذارد، بارگذاری را سرعت می‌بخشد. این کنترل، عملکرد کارآمد و محتوای به‌روز را تضمین می‌کند."

#: modules/element-cache/module.php:142
msgid "1 Year"
msgstr "۱ سال"

#: modules/element-cache/module.php:141
msgid "1 Month"
msgstr "۱ ماه"

#: modules/element-cache/module.php:140
msgid "2 Weeks"
msgstr "۲ هفته"

#: modules/element-cache/module.php:139
msgid "1 Week"
msgstr "۱ هفته"

#: modules/element-cache/module.php:138
msgid "3 Days"
msgstr "۳ روز"

#: modules/element-cache/module.php:137
msgid "1 Day"
msgstr "۱ روز"

#: modules/element-cache/module.php:136
msgid "12 Hours"
msgstr "۱۲ ساعت"

#: modules/element-cache/module.php:135
msgid "6 Hours"
msgstr "۶ ساعت"

#: modules/element-cache/module.php:134
msgid "1 Hour"
msgstr "۱ ساعت"

#. translators: %s: br
#: modules/promotions/admin-menu-items/form-submissions-promotion-item.php:25
msgid "Create Forms and Collect Leads %s with Elementor Pro"
msgstr "فرم‌ها را با المنتور Pro ایجاد کرده و سرنخ‌های %s را جمع‌آوری کنید"

#: modules/element-cache/module.php:106
msgid "Cache Settings"
msgstr "تنظیمات کش"

#: modules/floating-buttons/base/widget-contact-button-base.php:141
msgid "Contact Buttons"
msgstr "دکمه‌های تماس"

#: core/base/traits/shared-widget-controls-trait.php:107
msgid "Icons Per Row"
msgstr "آیکون‌ها در هر سطر"

#: modules/floating-buttons/classes/render/contact-buttons-render-base.php:222
msgid "Powered by Elementor"
msgstr "نیرو گرفته از المنتور"

#: core/base/providers/social-network-provider.php:240
msgid "Skype"
msgstr "اسکایپ"

#: core/base/providers/social-network-provider.php:234
msgid "Viber"
msgstr "وایبر"

#: core/base/providers/social-network-provider.php:228
msgid "SMS"
msgstr "پیامک"

#: core/base/providers/social-network-provider.php:222
msgid "File Download"
msgstr "دانلود فایل"

#: core/base/providers/social-network-provider.php:204
msgid "Telephone"
msgstr "تلفن"

#: core/base/providers/social-network-provider.php:198
msgid "Messenger"
msgstr "مسنجر"

#: core/base/providers/social-network-provider.php:192
msgid "Waze"
msgstr "ویز"

#: core/base/providers/social-network-provider.php:180
msgid "Dribbble"
msgstr "دریبل"

#: core/base/providers/social-network-provider.php:174
msgid "Behance"
msgstr "Behance"

#: core/base/providers/social-network-provider.php:162
msgid "Spotify"
msgstr "اسپاتیفای"

#: core/base/providers/social-network-provider.php:156
msgid "Apple Music"
msgstr "اپل موزیک"

#: core/base/providers/social-network-provider.php:150
msgid "WhatsApp"
msgstr "واتس‌اپ"

#: core/base/providers/social-network-provider.php:144
msgid "TikTok"
msgstr "تیک‌تاک"

#: core/base/providers/social-network-provider.php:132
msgid "Pinterest"
msgstr "پینترست"

#: core/base/providers/social-network-provider.php:126
msgid "LinkedIn"
msgstr "لینکدین"

#: core/base/providers/social-network-provider.php:120
msgid "Instagram"
msgstr "اینستاگرم"

#: core/base/providers/social-network-provider.php:114
msgid "X (Twitter)"
msgstr "X (توییتر)"

#: core/base/providers/social-network-provider.php:108
msgid "Facebook"
msgstr "فیس‌بوک"

#: core/base/providers/social-network-provider.php:102
msgid "Save contact (vCard)"
msgstr "ذخیره مخاطب (vCard)"

#: modules/link-in-bio/base/widget-link-in-bio-base.php:1031
#: modules/link-in-bio/base/widget-link-in-bio-base.php:1707
msgid "Image Height"
msgstr "ارتفاع تصویر"

#: modules/link-in-bio/base/widget-link-in-bio-base.php:1671
msgid "Image Shape"
msgstr "شکل تصویر"

#: modules/floating-buttons/base/widget-contact-button-base.php:2523
#: modules/link-in-bio/base/widget-link-in-bio-base.php:1196
msgid "Dividers"
msgstr "جدا کننده‌ها"

#: modules/link-in-bio/base/widget-link-in-bio-base.php:970
msgid "Image style"
msgstr "استایل تصویر"

#: modules/link-in-bio/base/widget-link-in-bio-base.php:883
msgid "About Me"
msgstr "درباره‌ی من"

#: modules/link-in-bio/base/widget-link-in-bio-base.php:882
msgid "About"
msgstr "درباره"

#: modules/link-in-bio/base/widget-link-in-bio-base.php:852
msgid "Sara Parker"
msgstr "سارا پارکر"

#: modules/link-in-bio/base/widget-link-in-bio-base.php:1626
msgid "Apply Full Screen Height on"
msgstr "اعمال ارتفاع تمام صفحه روی"

#: modules/link-in-bio/base/widget-link-in-bio-base.php:1609
msgid "Full Screen Height"
msgstr "ارتفاع تمام صفحه"

#: modules/link-in-bio/base/widget-link-in-bio-base.php:1562
msgid "Layout Width"
msgstr "عرض چیدمان"

#: modules/link-in-bio/base/widget-link-in-bio-base.php:797
msgid "Add Icon"
msgstr "افزودن آیکون"

#: modules/floating-buttons/base/widget-contact-button-base.php:2262
#: modules/link-in-bio/base/widget-link-in-bio-base.php:557
#: modules/link-in-bio/base/widget-link-in-bio-base.php:1381
msgid "Icons"
msgstr "آیکون‌ها"

#: modules/floating-buttons/base/widget-contact-button-base.php:918
#: modules/link-in-bio/base/widget-link-in-bio-base.php:532
#: modules/link-in-bio/base/widget-link-in-bio-base.php:780
msgid "Enter your username"
msgstr "نام‌کاربری خود را وارد کنید"

#: modules/link-in-bio/base/widget-link-in-bio-base.php:493
msgid "Enter your number"
msgstr "تلفن خود را وارد کنید"

#: modules/floating-buttons/base/widget-contact-button-base.php:836
#: modules/link-in-bio/base/widget-link-in-bio-base.php:438
#: modules/link-in-bio/base/widget-link-in-bio-base.php:679
msgid "Enter your email"
msgstr "ایمیل خود را وارد کنید"

#: modules/link-in-bio/base/widget-link-in-bio-base.php:427
msgid "Mail"
msgstr "نامه"

#: modules/link-in-bio/base/widget-link-in-bio-base.php:91
msgid "Top 10 Recipes"
msgstr "۱۰ دستور غذای برتر"

#: modules/link-in-bio/base/widget-link-in-bio-base.php:55
msgid "Join me on my journey to a healthier lifestyle"
msgstr "در سفرم به سمت یک سبک زندگی سالم به من بپیوندید"

#: modules/floating-buttons/base/widget-contact-button-base.php:2085
#: modules/floating-buttons/base/widget-contact-button-base.php:2176
#: modules/floating-buttons/base/widget-contact-button-base.php:2869
#: modules/floating-buttons/base/widget-floating-bars-base.php:846
#: modules/link-in-bio/base/widget-link-in-bio-base.php:120
msgid "Sharp"
msgstr "تیز"

#: modules/floating-buttons/base/widget-contact-button-base.php:2079
#: modules/floating-buttons/base/widget-contact-button-base.php:2170
#: modules/floating-buttons/base/widget-contact-button-base.php:2863
#: modules/floating-buttons/base/widget-floating-bars-base.php:840
#: modules/link-in-bio/base/widget-link-in-bio-base.php:1156
msgid "Corners"
msgstr "گوشه‌ها"

#: modules/floating-buttons/base/widget-contact-button-base.php:1797
msgid "Time"
msgstr "زمان"

#: modules/floating-buttons/base/widget-contact-button-base.php:1655
msgid "Close Button Color"
msgstr "رنگ دکمه بستن"

#: modules/floating-buttons/base/widget-contact-button-base.php:1641
#: modules/floating-buttons/base/widget-floating-bars-base.php:295
#: modules/floating-buttons/base/widget-floating-bars-base.php:1036
msgid "Close Button"
msgstr "دکمه بستن"

#: modules/floating-buttons/base/widget-contact-button-base.php:737
#: modules/floating-buttons/base/widget-contact-button-base.php:1045
msgid "Type your text here"
msgstr "متن خود را اینجا تایپ کنید"

#: modules/floating-buttons/base/widget-contact-button-base.php:682
msgid "14:20"
msgstr "14:20"

#: modules/floating-buttons/base/widget-contact-button-base.php:681
msgid "2:20 PM"
msgstr "2:20 PM"

#: modules/floating-buttons/base/widget-contact-button-base.php:677
msgid "Time format"
msgstr "فرمت زمانی"

#: modules/floating-buttons/base/widget-contact-button-base.php:669
msgid "Hey, how can I help you today?"
msgstr "سلام، امروز چگونه می‌توانم به شما کمک کنم؟"

#: modules/floating-buttons/base/widget-contact-button-base.php:655
msgid "Rob"
msgstr "راب"

#: modules/atomic-widgets/elements/atomic-heading/atomic-heading.php:61
#: modules/floating-buttons/base/widget-contact-button-base.php:131
msgid "Type your title here"
msgstr "عنوان خود را اینجا تایپ کنید"

#: modules/floating-buttons/base/widget-contact-button-base.php:123
#: modules/floating-buttons/base/widget-contact-button-base.php:656
msgid "Type your name here"
msgstr "نام خود را اینجا تایپ کنید"

#: modules/floating-buttons/base/widget-contact-button-base.php:122
msgid "Rob Jones"
msgstr "راب جونز"

#: modules/floating-buttons/base/widget-contact-button-base.php:380
#: modules/floating-buttons/base/widget-contact-button-base.php:980
msgid "Action"
msgstr "اقدام"

#: modules/floating-buttons/base/widget-contact-button-base.php:342
#: modules/floating-buttons/base/widget-contact-button-base.php:894
#: modules/link-in-bio/base/widget-link-in-bio-base.php:734
msgid "+"
msgstr "+"

#: modules/floating-buttons/base/widget-contact-button-base.php:321
#: modules/floating-buttons/base/widget-contact-button-base.php:663
#: modules/floating-buttons/base/widget-contact-button-base.php:670
#: modules/floating-buttons/base/widget-contact-button-base.php:870
#: modules/floating-buttons/base/widget-contact-button-base.php:872
#: modules/floating-buttons/base/widget-contact-button-base.php:1766
#: modules/link-in-bio/base/widget-link-in-bio-base.php:463
#: modules/link-in-bio/base/widget-link-in-bio-base.php:474
#: modules/link-in-bio/base/widget-link-in-bio-base.php:713
#: modules/link-in-bio/base/widget-link-in-bio-base.php:715
msgid "Message"
msgstr "پیغام"

#: modules/floating-buttons/base/widget-contact-button-base.php:305
#: modules/floating-buttons/base/widget-contact-button-base.php:855
#: modules/floating-buttons/base/widget-contact-button-base.php:857
#: modules/link-in-bio/base/widget-link-in-bio-base.php:445
#: modules/link-in-bio/base/widget-link-in-bio-base.php:456
#: modules/link-in-bio/base/widget-link-in-bio-base.php:698
#: modules/link-in-bio/base/widget-link-in-bio-base.php:700
msgid "Subject"
msgstr "موضوع"

#: modules/floating-buttons/base/widget-contact-button-base.php:294
msgid "@"
msgstr "@"

#: core/base/providers/social-network-provider.php:210
#: modules/floating-buttons/base/widget-contact-button-base.php:285
#: modules/floating-buttons/base/widget-contact-button-base.php:834
#: modules/link-in-bio/base/widget-link-in-bio-base.php:677
msgid "Email"
msgstr "ایمیل"

#: modules/apps/admin-apps-page.php:126
msgid "Cannot Install"
msgstr "نمی‌تواند نصب شود"

#: modules/apps/admin-apps-page.php:119 modules/apps/admin-apps-page.php:150
msgid "Cannot Activate"
msgstr "نمی‌تواند فعال شود"

#: core/settings/editor-preferences/model.php:233
msgid "Decide where you want to go when leaving the editor."
msgstr "هنگام خروج از ویرایشگر تصمیم بگیرید که کجا می‌خواهید بروید."

#: core/settings/editor-preferences/model.php:204
#: modules/styleguide/module.php:123
#: assets/js/styleguide-app.a6e297c616479b98c03d.bundle.js:442
msgid "Show global settings"
msgstr "نمایش تنظیمات سراسری"

#: modules/element-cache/module.php:42
msgid "Element Caching"
msgstr "حافظه پنهان عنصر"

#: modules/floating-buttons/base/widget-contact-button-base.php:183
msgid "Send Button"
msgstr "دکمه ارسال"

#: modules/floating-buttons/base/widget-contact-button-base.php:186
msgid "Click to start chat"
msgstr "برای شروع گفتگو کلیک کنید"

#: modules/element-cache/module.php:44
msgid "Elements caching reduces loading times by serving up a copy of an element instead of rendering it fresh every time the page is loaded. When active, Elementor will determine which elements can benefit from static loading - but you can override this."
msgstr "حافظه پنهان عناصر، وقتی که فعال باشد با ارائه نسخه‌ای از یک عنصر به جای رندر کردن مجدد آن در هر بار، زمان بارگذاری صفحه را کاهش می‌دهد. المنتور تعیین می‌کند که کدام عناصر می‌توانند از بارگذاری استاتیک بهره‌مند شوند - اما شما می‌توانید این تنظیمات را لغو کنید."

#: modules/floating-buttons/base/widget-contact-button-base.php:645
#: modules/floating-buttons/base/widget-contact-button-base.php:1712
msgid "Message Bubble"
msgstr "حباب پیغام"

#: modules/floating-buttons/base/widget-contact-button-base.php:65
msgid "Chat Button"
msgstr "دکمه گفتگو"

#: modules/floating-buttons/base/widget-contact-button-base.php:2774
msgid "Close Animation"
msgstr "انیمیشن بستن"

#: modules/floating-buttons/base/widget-contact-button-base.php:2764
msgid "Open Animation"
msgstr "انیمیشن باز کردن"

#: modules/floating-buttons/base/widget-contact-button-base.php:247
msgid "Chat Box"
msgstr "جعبه گفتگو"

#: modules/floating-buttons/base/widget-contact-button-base.php:613
#: modules/floating-buttons/base/widget-contact-button-base.php:1530
msgid "Profile Image"
msgstr "تصویر پروفایل"

#: modules/link-in-bio/base/widget-link-in-bio-base.php:974
msgid "Profile"
msgstr "پروفایل"

#: modules/link-in-bio/base/widget-link-in-bio-base.php:838
#: modules/link-in-bio/base/widget-link-in-bio-base.php:1284
msgid "Bio"
msgstr "بایو"

#: includes/settings/settings.php:454
msgid "Reduce unnecessary render-blocking loads by dequeuing unused Gutenberg block editor scripts and styles."
msgstr "با حذف اسکریپت‌ها و سبک‌های ویرایشگر بلوک استفاده نشده گوتنبرگ، بارهای غیرضروری render-blocking را کاهش دهید."

#: includes/managers/elements.php:306
msgid "Link In Bio"
msgstr "لینک در بایو"

#: includes/settings/settings.php:407
msgid "Improve loading times on your site by selecting the optimization tools that best fit your requirements."
msgstr "با انتخاب ابزارهای بهینه‌سازی که به بهترین وجه با نیازهای شما مطابقت دارند، زمان بارگذاری سایت خود را بهبود بخشید."

#. translators: 1: fetchpriority attribute, 2: lazy loading attribute.
#: includes/settings/settings.php:439
msgid "Improve performance by applying %1$s on LCP image and %2$s on images below the fold."
msgstr "عملکرد را با اعمال %1$s بر روی تصویر LCP و استفاده از %2$s بر روی تصاویر پایین‌تر از خطِ تا بهبود بخشید."

#: modules/link-in-bio/base/widget-link-in-bio-base.php:861
#: modules/link-in-bio/base/widget-link-in-bio-base.php:1320
msgid "Title or Tagline"
msgstr "عنوان یا شعار"

#: modules/link-in-bio/base/widget-link-in-bio-base.php:877
msgid "About Heading"
msgstr "تیتر درباره"

#: modules/floating-buttons/base/widget-contact-button-base.php:130
msgid "Store Manager"
msgstr "مدیر فروشگاه"

#: modules/floating-buttons/base/widget-contact-button-base.php:479
#: modules/floating-buttons/base/widget-contact-button-base.php:777
#: modules/link-in-bio/base/widget-link-in-bio-base.php:595
msgid "Platform"
msgstr "پلتفرم"

#: includes/widgets/traits/button-trait.php:299
#: assets/js/packages/editor-editing-panel/editor-editing-panel.js:8
#: assets/js/packages/editor-editing-panel/editor-editing-panel.strings.js:93
msgid "Space between"
msgstr "فاصله بین"

#: modules/floating-buttons/base/widget-contact-button-base.php:147
#: modules/link-in-bio/base/widget-link-in-bio-base.php:587
msgid "Enter icon text"
msgstr "متن آیکون را وارد کنید"

#: modules/link-in-bio/base/widget-link-in-bio-base.php:569
msgid "Add up to <b>%d</b> icons"
msgstr "تا <b>%d</b> آیکون اضافه کنید"

#: modules/link-in-bio/base/widget-link-in-bio-base.php:543
msgid "Add CTA Link"
msgstr "افزودن لینک CTA"

#: modules/link-in-bio/base/widget-link-in-bio-base.php:417
#: modules/link-in-bio/base/widget-link-in-bio-base.php:647
msgid "Enter your link"
msgstr "لینک خود را وارد کنید"

#: modules/link-in-bio/base/widget-link-in-bio-base.php:333
msgid "Enter link text"
msgstr "متن لینک را وارد کنید"

#: modules/link-in-bio/base/widget-link-in-bio-base.php:314
msgid "Add up to <b>%d</b> CTA links"
msgstr "تا <b>%d</b> لینک CTA اضافه کنید"

#: modules/link-in-bio/base/widget-link-in-bio-base.php:97
msgid "Healthy Living Resources"
msgstr "منابع زندگی سالم"

#: modules/link-in-bio/base/widget-link-in-bio-base.php:354
msgid "Link Type"
msgstr "نوع لینک"

#: modules/link-in-bio/base/widget-link-in-bio-base.php:88
msgid "Get Healthy"
msgstr "سالم بمانید"

#: modules/link-in-bio/base/widget-link-in-bio-base.php:94
msgid "Meal Prep"
msgstr "تهیه غذا"

#: modules/link-in-bio/base/widget-link-in-bio-base.php:911
#: modules/link-in-bio/base/widget-link-in-bio-base.php:1244
msgid "Identity"
msgstr "شناسه"

#. translators: %s: br
#: modules/promotions/admin-menu-items/custom-icons-promotion-item.php:25
msgid "Enjoy creative freedom %s with Custom Icons"
msgstr "با آیکون‌های سفارشی %s از آزادی در خلاقیت لذت ببرید."

#: modules/link-in-bio/base/widget-link-in-bio-base.php:52
msgid "Kitchen Chronicles"
msgstr "داستان‌های آشپزخانه"

#: modules/link-in-bio/base/widget-link-in-bio-base.php:302
#: modules/link-in-bio/base/widget-link-in-bio-base.php:1072
msgid "CTA Link Buttons"
msgstr "دکمه‌های لینک CTA"

#: core/settings/editor-preferences/model.php:165
msgid "This refers to elements you’ve hidden in the Responsive Visibility settings."
msgstr "این به عناصری اشاره دارد که در تنظیمات قابلیت مشاهده واکنش‌گرا پنهان کرده‌اید."

#: core/settings/editor-preferences/model.php:209
#: modules/styleguide/module.php:125
msgid "Temporarily overlay the canvas with the style guide to preview your changes to global colors and fonts."
msgstr "برای پیش‌نمایش تغییرات خود در رنگ‌ها و فونت‌های سراسری، بوم را به‌طور موقت با راهنمای استایل پوشش دهید."

#: modules/link-in-bio/base/widget-link-in-bio-base.php:1735
msgid "Bottom Border"
msgstr "حاشیه پایین"

#: core/admin/admin.php:1022 includes/controls/gallery.php:122
#: includes/controls/media.php:318
msgid "Optimize your images to enhance site performance by using Image Optimizer."
msgstr "با استفاده از بهینه‌ساز تصویر، تصاویر خود را برای بهبود عملکرد سایت بهینه کنید."

#: core/settings/editor-preferences/model.php:148
msgid "Expand images in lightbox"
msgstr "باز کردن تصاویر در لایت‌باکس"

#: core/settings/editor-preferences/model.php:84
msgid "Set light or dark mode, or auto-detect to sync with your operating system settings."
msgstr "تنظیم حالت روشن یا تاریک، یا تشخیص خودکار برای همگام‌سازی با تنظیمات سیستم‌عامل شما."

#: core/settings/editor-preferences/model.php:75
msgid "Dark mode"
msgstr "حالت تاریک"

#: core/settings/editor-preferences/model.php:71
msgid "Light mode"
msgstr "حالت روشن"

#: core/settings/editor-preferences/model.php:67
msgid "Display mode"
msgstr "حالت نمایش"

#: core/experiments/manager.php:371
msgid "Reduce the DOM size by eliminating HTML tags in various elements and widgets. This experiment includes markup changes so it might require updating custom CSS/JS code and cause compatibility issues with third party plugins."
msgstr "با حذف تگ‌های HTML در عناصر و ابزارک‌های مختلف، اندازه DOM را کاهش دهید. این آزمایش شامل تغییرات نشانه‌گذاری است، بنابراین ممکن است نیاز به به‌روزرسانی کدهای CSS/JS سفارشی داشته باشد و باعث مشکلات سازگاری با افزونه‌های شخص ثالث شود."

#: core/experiments/manager.php:369
msgid "Optimized Markup"
msgstr "نشانه‌گذاری بهینه شده"

#: core/settings/editor-preferences/model.php:59
msgid "Panel"
msgstr "پنل"

#: core/settings/editor-preferences/model.php:160
msgid "Show hidden elements"
msgstr "نمایش عناصر مخفی"

#: core/settings/editor-preferences/model.php:137
msgid "Show quick edit options"
msgstr "نمایش گزینه‌های ویرایش سریع"

#: core/settings/editor-preferences/model.php:128
msgid "Choose which device to display when clicking the Responsive Mode icon."
msgstr "انتخاب کنید هنگام کلیک کردن روی آیکون حالت واکنش‌گرا، کدام دستگاه نمایش داده شود."

#: core/settings/editor-preferences/model.php:141
msgid "Show additional actions while hovering over the handle of an element."
msgstr "نمایش اقدامات اضافی هنگام هاور روی دسته‌ای از یک عنصر."

#: core/settings/editor-preferences/model.php:153
msgid "This only applies while you’re working in the editor. The front end won’t be affected."
msgstr "این فقط زمانی اعمال می‌شود که در ویرایشگر کار می‌کنید. سمت کاربر تحت تاثیر قرار نخواهد گرفت."

#: modules/promotions/admin-menu-items/go-pro-promotion-item.php:33
msgid "Upgrade Sale Now"
msgstr "فروش ویژه ارتقا اکنون!"

#: core/admin/admin.php:349
msgid "Discounted Upgrades Now!"
msgstr "ارتقاهای تخفیف‌دار اکنون!"

#: core/admin/admin-notices.php:527
msgid "Automatically compress and optimize images, resize larger files, or convert to WebP. Optimize images individually, in bulk, or on upload."
msgstr "فشرده‌سازی و بهینه‌سازی خودکار تصاویر، تغییر اندازه فایل‌های بزرگتر یا تبدیل به WebP. تصاویر را به صورت جداگانه، دسته‌جمعی یا هنگام آپلود بهینه کنید."

#: core/admin/admin-notices.php:526
msgid "Speed up your website with Image Optimizer by Elementor"
msgstr "سرعت وب‌سایت خود را با Image Optimizer by Elementor افزایش دهید"

#: modules/home/<USER>
msgid "Default Elementor menu page."
msgstr "صفحه منوی پیش‌فرض المنتور."

#: modules/home/<USER>
msgid "Elementor Home Screen"
msgstr "صفحه اصلی المنتور"

#: includes/widgets/counter.php:452
msgid "Number Gap"
msgstr "شکاف عدد"

#: includes/widgets/counter.php:424
msgid "Number Alignment"
msgstr "تراز عدد"

#: includes/widgets/counter.php:388
msgid "Number Position"
msgstr "موقعیت عدد"

#: includes/widgets/counter.php:372
msgid "Title Gap"
msgstr "شکاف عنوان"

#: includes/widgets/counter.php:343
msgid "Title Vertical Alignment"
msgstr "تراز عمودی عنوان"

#: includes/widgets/counter.php:314
msgid "Title Horizontal Alignment"
msgstr "تراز افقی عنوان"

#: includes/widgets/counter.php:275
msgid "Title Position"
msgstr "موقعیت عنوان"

#: includes/settings/settings.php:215
msgid "Home"
msgstr "خانه"

#: elementor.php:96
msgid "Elementor isn’t running because WordPress is outdated."
msgstr "المنتور اجرا نمی‌شود زیرا وردپرس قدیمی است."

#. translators: %s: PHP version.
#. translators: %s: WordPress version.
#: elementor.php:75 elementor.php:99
msgid "Update to version %s and get back to creating!"
msgstr "به نسخه %s به‌روز رسانی کنید و به ایجاد بازگردید!"

#: elementor.php:72
msgid "Elementor isn’t running because PHP is outdated."
msgstr "المنتور اجرا نمی‌شود زیرا PHP قدیمی است."

#: core/files/uploads-manager.php:589
msgid "You do not have permission to upload JSON files."
msgstr "شما اجازه آپلود فایل‌های JSON را ندارید."

#: core/admin/admin.php:1024 modules/apps/admin-apps-page.php:123
msgid "Install"
msgstr "نصب"

#: core/admin/admin.php:1024
msgid "Image Optimizer"
msgstr "بهینه‌ساز تصویر"

#: modules/site-navigation/rest-fields/page-user-can.php:28
msgid "Whether the current user can edit or delete this post"
msgstr "آیا کاربر فعلی می‌تواند این پست را ویرایش یا حذف کند"

#: modules/shapes/widgets/text-path.php:149
msgid "Want to create custom text paths with SVG?"
msgstr "آیا می‌خواهید مسیرهای متنی سفارشی با SVG ایجاد کنید؟"

#: modules/promotions/admin-menu-items/custom-icons-promotion-item.php:36
msgid "Add any icon, anywhere on your website"
msgstr "هر آیکونی را در هر جایی از وب‌سایت خود اضافه کنید"

#: modules/promotions/admin-menu-items/custom-icons-promotion-item.php:33
msgid "Expand your icon library beyond FontAwesome and add icon %s libraries of your choice"
msgstr "کتابخانه آیکون خود را فراتر از FontAwesome گسترش دهید و آیکون %s را به انتخاب خود اضافه کنید"

#. translators: %s: br
#: modules/promotions/admin-menu-items/custom-fonts-promotion-item.php:31
msgid "Remain GDPR compliant with Custom Fonts that let you disable %s Google Fonts from your website"
msgstr "با فونت‌های سفارشی که به شما امکان می‌دهد %s فونت گوگل را از وب سایت خود غیرفعال کنید، مطابق با GDPR باقی بمانید."

#: modules/promotions/admin-menu-items/custom-fonts-promotion-item.php:28
msgid "Upload any font to keep your website true to your brand"
msgstr "هر فونتی را آپلود کنید تا وب‌سایت شما مطابق با برند شما باشد"

#: modules/promotions/admin-menu-items/custom-fonts-promotion-item.php:23
msgid "Stay on brand with a Custom Font"
msgstr "با یک فونت سفارشی، با برند خود همسو باشید"

#: modules/promotions/admin-menu-items/custom-code-promotion-item.php:30
msgid "Leverage Elementor AI to instantly generate Custom Code for Elementor"
msgstr "از هوش مصنوعی المنتور برای ایجاد فوری کد سفارشی برای المنتور استفاده کنید"

#: modules/promotions/admin-menu-items/custom-code-promotion-item.php:29
msgid "Use Custom Code to create sophisticated custom interactions to engage visitors"
msgstr "از کد سفارشی برای ایجاد تعاملات سفارشی پیچیده برای جذب بازدیدکنندگان استفاده کنید"

#: modules/promotions/admin-menu-items/custom-code-promotion-item.php:28
msgid "Add Custom Code snippets anywhere on your website, including the header or footer to measure your page’s performance*"
msgstr "برای اندازه‌گیری بازدهی صفحه خود، تکه کدهای سفارشی را در هر جایی از وب‌سایت خود اضافه کنید، از جمله سربرگ یا پابرگ*"

#: modules/apps/admin-pointer.php:35
msgid "Explore Add-ons"
msgstr "کاوش افزودنی‌ها"

#: modules/apps/admin-pointer.php:29
msgid "New! Popular Add-ons"
msgstr "تازه! افزودنی‌های محبوب"

#: modules/apps/admin-menu-apps.php:22 modules/apps/admin-menu-apps.php:26
#: modules/apps/module.php:37 assets/js/admin-top-bar.js:185
#: assets/js/editor.js:38269
msgid "Add-ons"
msgstr "افزودنی‌ها"

#: modules/apps/admin-apps-page.php:35
msgid "Please note that certain tools and services on this page are developed by third-party companies and are not part of Elementor's suite of products or support. Before using them, we recommend independently evaluating them. Additionally, when clicking on their action buttons, you may be redirected to an external website."
msgstr "لطفا توجه داشته باشید که برخی از ابزارها و خدمات موجود در این صفحه توسط شرکت‌های شخص ثالث توسعه یافته‌اند و جزیی از مجموعه محصولات یا پشتیبانی المنتور نیستند. قبل از استفاده از آنها، توصیه می‌کنیم به طور مستقل آنها را ارزیابی کنید."

#: modules/promotions/admin-menu-items/custom-code-promotion-item.php:23
msgid "Enjoy Creative Freedom with Custom Code"
msgstr "با کد سفارشی، از آزادی خلاقانه لذت ببرید"

#: modules/apps/module.php:70
msgid "For Elementor"
msgstr "برای المنتور"

#: includes/widgets/text-editor.php:191
msgid "10"
msgstr "۱۰"

#: includes/widgets/text-editor.php:190
msgid "9"
msgstr "۹"

#: includes/widgets/text-editor.php:189
msgid "8"
msgstr "۸"

#: includes/widgets/text-editor.php:188
msgid "7"
msgstr "۷"

#: includes/widgets/text-editor.php:187
msgid "6"
msgstr "۶"

#: includes/widgets/text-editor.php:186
msgid "5"
msgstr "۵"

#: includes/widgets/text-editor.php:185
msgid "4"
msgstr "۴"

#: includes/widgets/text-editor.php:184
msgid "3"
msgstr "۳"

#: includes/widgets/text-editor.php:183
msgid "2"
msgstr "۲"

#: includes/widgets/text-editor.php:182
msgid "1"
msgstr "۱"

#: includes/widgets/icon-box.php:352 includes/widgets/image-box.php:324
msgid "Content Spacing"
msgstr "فاصله محتوا"

#: includes/widgets/common-base.php:1028
msgid "Explore additional Premium Shape packs and use them in your site."
msgstr "بسته‌های Premium Shape اضافی را کاوش کنید و از آنها در سایت خود استفاده کنید."

#. translators: 1: Link open tag, 2: Link close tag.
#: includes/settings/tools.php:344
msgid "It is strongly recommended to %1$sbackup the database%2$s before using replacing URLs."
msgstr "اکیدا توصیه می‌شود که قبل استفاده از جایگزینی URL ها %1$sاز پایگاه داده نسخه پشتیبان تهیه کنید%2$s."

#: core/role-manager/role-manager.php:213
msgid "Enable the option to use the HTML widget"
msgstr "گزینه استفاده از ابزارک HTML را فعال کنید"

#: includes/controls/base-units.php:138
msgid "Custom unit"
msgstr "واحد سفارشی"

#: core/role-manager/role-manager.php:215
msgid "Giving broad access to edit the HTML widget can pose a security risk to your website because it enables users to run malicious scripts, etc."
msgstr "دادن دسترسی گسترده به ویرایش ابزارک HTML می‌تواند خطر امنیتی برای وب‌سایت شما ایجاد کند زیرا به کاربران امکان می دهد اسکریپت‌های مخرب و غیره اجرا کنند."

#: includes/editor-templates/panel-elements.php:33
msgid "Access all Pro widgets."
msgstr "دسترسی به تمام ابزارک‌های حرفه‌ای."

#: includes/editor-templates/navigator.php:19
msgid "Access all Pro widgets"
msgstr "دسترسی به تمام ابزارک‌های حرفه‌ای"

#: core/utils/hints.php:156 includes/controls/notice.php:83
msgid "Don’t show again."
msgstr "دیگر نشان داده نشود."

#: includes/managers/controls.php:1319
#: modules/floating-buttons/base/widget-floating-bars-base.php:1453
#: assets/js/editor.js:52878
#: assets/js/packages/editor-editing-panel/editor-editing-panel.js:8
#: assets/js/packages/editor-editing-panel/editor-editing-panel.strings.js:79
msgid "Sticky"
msgstr "چسبنده"

#: includes/managers/controls.php:1310 assets/js/editor.js:52863
msgid "Mouse Effects"
msgstr "جلوه های ماوس"

#: includes/managers/controls.php:1301 assets/js/editor.js:52848
msgid "Scrolling Effects"
msgstr "جلوه های اسکرول"

#: core/admin/admin-notices.php:479 includes/controls/gallery.php:123
#: includes/controls/media.php:319
msgid "Install Plugin"
msgstr "نصب افزونه"

#: modules/promotions/admin-menu-items/custom-code-promotion-item.php:35
#: modules/promotions/admin-menu-items/form-submissions-promotion-item.php:48
msgid "* Requires an Advanced subscription or higher"
msgstr "* به اشتراک پیشرفته یا بالاتر نیاز دارد"

#: modules/promotions/admin-menu-items/form-submissions-promotion-item.php:35
msgid "Collect lead submissions directly within your WordPress Admin to manage, analyze and perform bulk actions on the submitted lead*"
msgstr "برای مدیریت، تجزیه و تحلیل و انجام اقدامات انبوه روی سرنخ دریافتی، ورودی‌های سرنخ را مستقیما در مدیریت وردپرس خود جمع‌آوری کنید*"

#: modules/promotions/admin-menu-items/form-submissions-promotion-item.php:34
msgid "Integrate your favorite marketing software*"
msgstr "نرم‌افزار بازاریابی مورد علاقه خود را یکپارچه کنید*"

#: modules/promotions/admin-menu-items/form-submissions-promotion-item.php:33
msgid "Use any field to collect the information you need"
msgstr "از هر فیلدی برای جمع‌آوری اطلاعات مورد نیاز خود استفاده کنید"

#: modules/promotions/admin-menu-items/form-submissions-promotion-item.php:32
msgid "Create single or multi-step forms to engage and convert visitors"
msgstr "برای جذب و تبدیل بازدیدکنندگان، فرم‌های تک یا چند مرحله‌ای ایجاد کنید"

#: includes/managers/controls.php:1270 assets/js/editor.js:52833
msgid "Display Conditions"
msgstr "‫شرایط نمایش"

#: includes/widgets/testimonial.php:176
msgid "Designer"
msgstr "طراح"

#: includes/widgets/testimonial.php:161
msgid "John Doe"
msgstr "John Doe"

#: includes/controls/groups/background.php:162
msgid "Set locations and angle for each breakpoint to ensure the gradient adapts to different screen sizes."
msgstr "موقعیت‌ها و زاویه را برای هر نقطه شکست تنظیم کنید تا اطمینان حاصل کنید که گرادیان با اندازه‌های مختلف صفحه نمایش سازگار است."

#: includes/editor-templates/templates.php:277
msgid "Generate Variations"
msgstr "تولید تنوع‌ها"

#: includes/template-library/manager.php:405
#: includes/template-library/sources/local.php:808
msgid "You do not have permission to access this template."
msgstr "شما اجازه دسترسی به این قالب را ندارید."

#: includes/template-library/sources/local.php:812
msgid "You do not have permission to export this template."
msgstr "شما اجازه برون‌بری این قالب را ندارید."

#: includes/template-library/sources/local.php:803
msgid "Invalid template type or template does not exist."
msgstr "نوع قالب نامعتبر است یا قالب وجود ندارد."

#: core/role-manager/role-manager.php:197
msgid "Giving broad access to upload JSON files can pose a security risk to your website because such files may contain malicious scripts, etc."
msgstr "دادن دسترسی گسترده به آپلود فایل‌های JSON می‌تواند خطر امنیتی برای وب‌سایت شما ایجاد کند، زیرا ممکن است چنین فایل‌هایی حاوی اسکریپت‌های مخرب و غیره باشند."

#: core/role-manager/role-manager.php:197
#: core/role-manager/role-manager.php:215
msgid "Heads up"
msgstr "مراقب باشید"

#: core/role-manager/role-manager.php:195
msgid "Enable the option to upload JSON files"
msgstr "گزینه آپلود فایل‌های JSON را فعال کنید"

#: core/files/uploads-manager.php:290
msgid "Invalid file name."
msgstr "نام فایل نامعتبر است."

#: modules/element-manager/ajax.php:127
msgid "WordPress Widgets"
msgstr "‫ابزارک‌های وردپرس"

#: modules/element-manager/ajax.php:148
msgid "No elements to save."
msgstr "هیچ عنصری برای ذخیره‌شدن نیست."

#: modules/element-manager/ajax.php:112
msgid "You do not have permission to edit these settings."
msgstr "شما اجازه ویرایش این تنظیمات را ندارید."

#: includes/template-library/sources/local.php:238
msgid "Parent Template:"
msgstr "قالب والد:"

#: includes/template-library/sources/local.php:237
msgid "No Templates found in Trash"
msgstr "‫هیچ قالبی در زباله‌دان پیدا نشد"

#: includes/template-library/sources/local.php:236
msgid "No Templates found"
msgstr "هیچ قالبی پیدا نشد"

#: includes/template-library/sources/local.php:235
msgid "Search Template"
msgstr "جستجوی قالب"

#: includes/template-library/sources/local.php:234
msgid "View Template"
msgstr "مشاهده قالب"

#: includes/template-library/sources/local.php:233
msgid "All Templates"
msgstr "همه قالب‌ها"

#: includes/template-library/sources/local.php:229
#: includes/template-library/sources/local.php:230
msgid "Add New Template"
msgstr "افزودن قالب جدید"

#: includes/controls/groups/image-size.php:296 includes/controls/media.php:297
#: includes/widgets/testimonial.php:317
#: assets/js/packages/editor-controls/editor-controls.js:8
msgid "Image Resolution"
msgstr "رزولوشن تصویر"

#: includes/widgets/image-gallery.php:108
msgid "Use interesting masonry layouts and other overlay features with Elementor's Pro Gallery widget."
msgstr "با ویجت گالری المنتور حرفه ای از طرح‌بندی‌های جالب بنایی و سایر ویژگی‌های پوشش استفاده کنید."

#: core/common/modules/finder/categories/settings.php:79
#: modules/element-manager/admin-menu-app.php:22
#: modules/element-manager/admin-menu-app.php:26
#: modules/element-manager/admin-menu-app.php:35
msgid "Element Manager"
msgstr "مدیریت عنصر"

#: modules/promotions/widgets/pro-widget-promotion.php:56
msgid "This result includes the Elementor Pro %s widget. Upgrade now to unlock it and grow your web creation toolkit."
msgstr "این نتیجه شامل ابزارک %s المنتور پرو است. اکنون ارتقا دهید تا قفل آن را باز کنید و جعبه ابزار ایجاد وب خود را توسعه دهید."

#: modules/element-manager/ajax.php:154
msgid "Unexpected elements data."
msgstr "داده‌های عناصر غیر منتظره."

#: includes/controls/groups/flex-container.php:183
#: includes/controls/groups/flex-container.php:191
#: assets/js/packages/editor-editing-panel/editor-editing-panel.js:8
#: assets/js/packages/editor-editing-panel/editor-editing-panel.strings.js:86
#: assets/js/packages/editor-editing-panel/editor-editing-panel.strings.js:88
msgid "Wrap"
msgstr "Wrap"

#: includes/controls/groups/flex-container.php:187
msgid "No Wrap"
msgstr "No Wrap"

#: modules/element-manager/ajax.php:117
msgid "Invalid nonce."
msgstr "کلید نامعتبر."

#: core/experiments/manager.php:361
msgid "Container-based content will be hidden from your site and may not be recoverable in all cases."
msgstr "محتوای مبتنی بر کانتینر از سایت شما پنهان می‌شود و ممکن است همه موارد قابل بازیابی نباشد."

#: includes/settings/settings.php:446
msgid "Optimized Gutenberg Loading"
msgstr "بارگذاری بهینه گوتنبرگ"

#. translators: %s: Recommended PHP version.
#: modules/system-info/reporters/server.php:131
msgid "We recommend using PHP version %s or higher."
msgstr "توصیه می‌کنیم از PHP نسخه %s یا بالاتر استفاده کنید."

#: modules/page-templates/module.php:158
msgid "Elementor Full Width"
msgstr "تمام عرض المنتور"

#: modules/page-templates/module.php:157
msgid "Elementor Canvas"
msgstr "Canvas المنتور"

#: modules/nested-accordion/widgets/nested-accordion.php:320
msgid "Let Google know that this section contains an FAQ. Make sure to only use it only once per page"
msgstr "به گوگل اطلاع دهید که این بخش حاوی یک سوالات متداول است. مطمئن شوید که در هر صفحه فقط یک بار از آن استفاده کنید"

#: modules/image-loading-optimization/module.php:240
msgid "An image should not be lazy-loaded and marked as high priority at the same time."
msgstr "یک تصویر نمی‌تواند همزمان بارگذاری تنبل بوده و به عنوان اولویت بالا هم باشد."

#: includes/widgets/video.php:291
msgid "VideoPress URL"
msgstr "URL ویدئوپرس"

#: includes/widgets/video.php:140
msgid "VideoPress"
msgstr "ویدئوپرس"

#: includes/widgets/star-rating.php:129
msgid "You are currently editing a Star Rating widget in its old version. Drag a new Rating widget onto your page to use a newer version, providing better capabilities."
msgstr "شما اکنون در حال ویرایش یک ویجت ستاره امتیاز دهی در نسخه قدیمی آن هستید. یک ویجت جدید امتیاز دهی را روی صفحه خود بکشید تا از نسخه جدیدتر که قابلیت‌های بهتری را ارائه می‌دهد، استفاده کنید."

#: includes/widgets/rating.php:308
msgid "Rated %1$s out of %2$s"
msgstr "امتیاز %1$s از %2$s"

#: includes/settings/settings.php:429
msgid "Optimized Image Loading"
msgstr "بارگذاری بهینه تصویر"

#: includes/settings/tools.php:165
msgid "An error occurred, the selected version is invalid. Try selecting different version."
msgstr "خطایی روی داده، نسخه انتخابی نامعتبر است. نسخه‌ی متفاوتی را امتحان کنید."

#: includes/controls/groups/typography.php:220
#: assets/js/packages/editor-editing-panel/editor-editing-panel.js:8
#: assets/js/packages/editor-editing-panel/editor-editing-panel.strings.js:53
msgid "Letter Spacing"
msgstr "فاصله حروف"

#: includes/controls/groups/typography.php:198
#: assets/js/packages/editor-editing-panel/editor-editing-panel.js:8
#: assets/js/packages/editor-editing-panel/editor-editing-panel.strings.js:52
msgid "Line Height"
msgstr "ارتفاع خط"

#: includes/controls/groups/text-shadow.php:61
#: includes/controls/groups/text-shadow.php:85
msgid "Text Shadow"
msgstr "سایه متن"

#: includes/controls/groups/image-size.php:380
#: modules/atomic-widgets/image/image-sizes.php:38
#: assets/js/packages/editor-controls/editor-controls.js:10
msgid "Full"
msgstr "کامل"

#: includes/controls/groups/image-size.php:301
msgid "Image Dimension"
msgstr "ابعاد تصویر"

#: includes/controls/groups/grid-container.php:131
msgid "Justify Items"
msgstr "تراز کردن آیتم‌ها"

#: includes/controls/groups/flex-item.php:173
msgid "Flex Shrink"
msgstr "Flex Shrink"

#: includes/controls/groups/flex-item.php:159
msgid "Flex Grow"
msgstr "Flex Grow"

#: includes/controls/groups/flex-item.php:138
#: assets/js/packages/editor-editing-panel/editor-editing-panel.js:8
#: assets/js/packages/editor-editing-panel/editor-editing-panel.strings.js:99
#: assets/js/packages/editor-editing-panel/editor-editing-panel.strings.js:103
msgid "Shrink"
msgstr "Shrink"

#: includes/controls/groups/flex-item.php:134
#: assets/js/packages/editor-editing-panel/editor-editing-panel.js:8
#: assets/js/packages/editor-editing-panel/editor-editing-panel.strings.js:98
#: assets/js/packages/editor-editing-panel/editor-editing-panel.strings.js:102
msgid "Grow"
msgstr "Grow"

#: includes/controls/groups/flex-item.php:113
msgid "Custom Order"
msgstr "ترتیب دلخواه"

#: includes/controls/groups/flex-item.php:76
#: includes/controls/groups/flex-item.php:109
msgid "This control will affect contained elements only."
msgstr "این کنترل فقط روی عناصر موجود تاثیر می‌گذارد."

#: includes/controls/groups/flex-item.php:20
msgid "Flex Basis"
msgstr "Flex Basis"

#: includes/controls/groups/flex-container.php:195
msgid "Items within the container can stay in a single line (No wrap), or break into multiple lines (Wrap)."
msgstr "اقلام داخل کانتینر می‌توانند در یک خط باقی بمانند (No wrap)، یا به چند خط تقسیم شوند (Wrap)."

#: includes/controls/groups/flex-container.php:91
#: includes/controls/groups/grid-container.php:186
msgid "Justify Content"
msgstr "تراز کردن محتوا"

#: includes/controls/groups/flex-container.php:45
msgid "Column - reversed"
msgstr "ستون - معکوس شده"

#: includes/controls/groups/flex-container.php:41
msgid "Row - reversed"
msgstr "سطر - معکوس شده"

#: includes/controls/groups/flex-container.php:37
msgid "Column - vertical"
msgstr "ستون - عمودی"

#: includes/controls/groups/flex-container.php:33
msgid "Row - horizontal"
msgstr "ردیف - افقی"

#: includes/controls/groups/css-filter.php:162
msgid "CSS Filters"
msgstr "فیلترهای CSS"

#: includes/controls/groups/box-shadow.php:61
#: includes/controls/groups/box-shadow.php:96
msgid "Box Shadow"
msgstr "سایه جعبه"

#: includes/controls/groups/border.php:69
#: assets/js/packages/editor-editing-panel/editor-editing-panel.js:8
#: assets/js/packages/editor-editing-panel/editor-editing-panel.strings.js:140
msgid "Groove"
msgstr "Groove"

#: includes/controls/groups/border.php:60
#: assets/js/packages/editor-editing-panel/editor-editing-panel.js:8
#: assets/js/packages/editor-editing-panel/editor-editing-panel.strings.js:144
msgid "Border Type"
msgstr "نوع حاشیه"

#: includes/controls/groups/background.php:607
msgid "Background Fallback"
msgstr "پس‌زمینه جایگزین"

#: includes/controls/groups/background.php:478
msgid "Display Size"
msgstr "اندازه نمایش"

#: includes/controls/groups/background.php:301
msgid "Background Image"
msgstr "تصویر پس‌زمینه"

#: includes/controls/groups/background.php:245
msgid "Angle"
msgstr "زاویه‌دار"

#: includes/controls/groups/background.php:234
msgid "Radial"
msgstr "دایره‌ای"

#: includes/controls/groups/background.php:233
msgid "Linear"
msgstr "خطی"

#: includes/controls/groups/background.php:201
msgid "Second Color"
msgstr "رنگ دوم"

#: includes/controls/groups/background.php:154
msgid "Background Type"
msgstr "نوع پس‌زمینه"

#: includes/controls/groups/background.php:95
msgid "Classic"
msgstr "کلاسیک"

#: includes/controls/groups/flex-item.php:51
msgid "Align Self"
msgstr "Align Self"

#: includes/controls/groups/flex-container.php:204
#: includes/controls/groups/grid-container.php:226
msgid "Align Content"
msgstr "تراز محتوا"

#: includes/controls/groups/flex-container.php:128
#: includes/controls/groups/grid-container.php:159
msgid "Align Items"
msgstr "تراز موارد"

#: app/modules/onboarding/module.php:133
msgid "You do not have permission to perform this action."
msgstr "شما مجاز به انجام این کار نیستید."

#: modules/nested-tabs/widgets/nested-tabs.php:1170
#: modules/nested-tabs/widgets/nested-tabs.php:1235
msgid "Tabs. Open items with Enter or Space, close with Escape and navigate using the Arrow keys."
msgstr "زبانه‌ها. موارد را با Enter یا Space باز کنید، با Escape ببندید و با استفاده از کلیدهای جهت‌نما پیمایش کنید."

#. translators: %s: Post type (e.g. Page, Post, etc.)
#: core/document-types/page-base.php:186
#: assets/js/packages/editor-site-navigation/editor-site-navigation.js:2
#: assets/js/packages/editor-site-navigation/editor-site-navigation.strings.js:13
msgid "View %s"
msgstr "مشاهده %s"

#: modules/nested-tabs/widgets/nested-tabs.php:199
#: modules/nested-tabs/widgets/nested-tabs.php:869
msgid "Below"
msgstr "پایین"

#: modules/nested-tabs/widgets/nested-tabs.php:195
#: modules/nested-tabs/widgets/nested-tabs.php:861
msgid "Above"
msgstr "بالا"

#: modules/site-navigation/module.php:69
msgid "Pages Panel"
msgstr "پنل صفحات"

#: includes/editor-templates/templates.php:245 assets/js/editor.js:10426
#: assets/js/packages/editor-site-navigation/editor-site-navigation.js:2
#: assets/js/packages/editor-site-navigation/editor-site-navigation.strings.js:15
msgid "Rename"
msgstr "تغییر نام"

#: modules/nested-accordion/widgets/nested-accordion.php:401
msgid "Space between Items"
msgstr "فاصله بین آیتم ها"

#: modules/nested-accordion/widgets/nested-accordion.php:357
msgid "Multiple"
msgstr "چندگانه"

#: modules/nested-accordion/widgets/nested-accordion.php:356
msgid "One"
msgstr "یک"

#: modules/nested-accordion/widgets/nested-accordion.php:353
msgid "Max Items Expanded"
msgstr "حداکثر موارد گسترش یافته است"

#: modules/nested-accordion/widgets/nested-accordion.php:339
msgid "Default State"
msgstr "وضعیت پیش‌فرض"

#: includes/widgets/video.php:929
msgid "Note: These controls have been deprecated and are only visible if they were previously in use. The video’s width and position are now set based on its aspect ratio."
msgstr "توجه: این کنترل‌ها منسوخ شده‌اند و فقط در صورتی قابل مشاهده هستند که قبلاً استفاده شده باشند. عرض و موقعیت ویدیو اکنون بر اساس نسبت ابعاد آن تنظیم شده است."

#: includes/widgets/video.php:234
msgid "Choose Video File"
msgstr "فایل ویدیویی را انتخاب کنید"

#. translators: 1: Slide count, 2: Total slides count.
#: includes/widgets/image-carousel.php:969
msgid "%1$s of %2$s"
msgstr "%1$s از %2$s"

#: includes/widgets/icon-box.php:229 includes/widgets/image-box.php:204
#: modules/link-in-bio/base/widget-link-in-bio-base.php:1453
msgid "Box"
msgstr "جعبه"

#. translators: 1: Link open tag, 2: Link open tag, 3: Link close tag.
#: core/kits/documents/tabs/settings-site-identity.php:60
msgid "Changes will be reflected only after %1$s saving %3$s and %2$s reloading %3$s preview."
msgstr "تغییرات فقط پس از %1$s ذخیره %3$s و %2$s بارگیری مجدد %3$s پیش‌نمایش منعکس می‌شوند."

#: core/admin/admin.php:628
msgid "Build Smart with AI"
msgstr "با هوش مصنوعی هوشمند بسازید"

#: modules/nested-accordion/widgets/nested-accordion.php:168
msgid "Item Position"
msgstr "موقعیت آیتم"

#: modules/apps/admin-apps-page.php:27
msgid "Learn more about this page."
msgstr "درباره این برگه بیشتر بدانید"

#: modules/apps/admin-apps-page.php:26
msgid "Boost your web-creation process with add-ons, plugins, and more tools specially selected to unleash your creativity, increase productivity, and enhance your Elementor-powered website."
msgstr "فرآیند ایجاد وب خود را با افزودنی‌ها، افزونه‌ها و ابزارهای بیشتری که به طور خاص، برای تراوش خلاقیت، افزایش بهره‌وری و ارتقای وب‌سایت مبتنی بر المنتور انتخاب شده‌اند، تقویت کنید."

#: modules/nested-accordion/widgets/nested-accordion.php:252
msgid "Collapse"
msgstr "بسته"

#: modules/nested-accordion/widgets/nested-accordion.php:238
#: assets/js/ai-admin.js:9394 assets/js/ai-gutenberg.js:11242
#: assets/js/ai-layout.js:4875 assets/js/ai-media-library.js:11023
#: assets/js/ai-unify-product-images.js:11023 assets/js/ai.js:12470
#: assets/js/packages/editor-editing-panel/editor-editing-panel.strings.js:25
msgid "Expand"
msgstr "باز"

#: modules/nested-accordion/widgets/nested-accordion.php:343
msgid "All collapsed"
msgstr "همه بسته"

#: modules/nested-accordion/widgets/nested-accordion.php:342
msgid "First expanded"
msgstr "اولی باز"

#: modules/apps/admin-pointer.php:30
msgid "Discover our collection of plugins and add-ons carefully selected to enhance your Elementor website and unleash your creativity."
msgstr "مجموعه افزونه‌ها و افزودنی‌های ما را که با دقت انتخاب شده‌اند، کشف کنید تا وب‌سایت المنتوری خود را ارتقا دهید و خلاقیت خود را آزاد کنید."

#: modules/nested-accordion/widgets/nested-accordion.php:332
msgid "Interactions"
msgstr "تعاملات"

#: modules/apps/admin-apps-page.php:25
msgid "Popular Add-ons, New Possibilities."
msgstr "افزودنی‌های محبوب، امکانات جدید."

#: modules/nested-tabs/widgets/nested-tabs.php:377
msgid "Horizontal Scroll"
msgstr "پیمایش افقی"

#: modules/floating-buttons/base/widget-floating-bars-base.php:376
#: modules/nested-accordion/widgets/nested-accordion.php:157
msgid "Item #3"
msgstr "گزینه #3"

#: modules/floating-buttons/base/widget-floating-bars-base.php:373
#: modules/nested-accordion/widgets/nested-accordion.php:154
msgid "Item #2"
msgstr "گزینه #2"

#: modules/floating-buttons/base/widget-floating-bars-base.php:370
#: modules/nested-accordion/widgets/nested-accordion.php:151
msgid "Item #1"
msgstr "گزینه #1"

#: modules/floating-buttons/base/widget-floating-bars-base.php:340
#: modules/nested-accordion/widgets/nested-accordion.php:117
#: modules/nested-accordion/widgets/nested-accordion.php:118
msgid "Item Title"
msgstr "عنوان گزینه"

#: modules/nested-accordion/widgets/nested-accordion.php:66
msgid "item #%s"
msgstr "گزینه #%s"

#: includes/widgets/image.php:398
msgid "Object Position"
msgstr "موقعیت شیء"

#: includes/frontend.php:1421
msgid "Next slide"
msgstr "اسلاید بعدی"

#: includes/frontend.php:1420
msgid "Previous slide"
msgstr "اسلاید قبلی"

#: includes/frontend.php:1424
msgid "Go to slide"
msgstr "رفتن به اسلاید"

#: includes/editor-templates/hotkeys.php:94
msgid "Panels"
msgstr "پنل‌ها"

#: includes/controls/gallery.php:84 assets/js/editor.js:14800
msgid "Clear gallery"
msgstr "پاک کردن گالری"

#: modules/nested-tabs/widgets/nested-tabs.php:379
msgid "Note: Scroll tabs if they don’t fit into their parent container."
msgstr "توجه: اگر برگه‌ها در محفظه اصلی خود جا نمی‌شوند، اسکرول کنید."

#: includes/widgets/toggle.php:157
msgid "You are currently editing a Toggle widget in its old version. Drag a new Accordion widget onto your page to use a newer version, providing nested capabilities."
msgstr "شما در حال حاضر در حال ویرایش یک ویجت تغییر وضعیت در نسخه قدیمی آن هستید. یک ویجت جدید آکاردئون را روی صفحه خود بکشید تا از نسخه جدیدتر استفاده کنید و قابلیت های تو در تو را ارائه دهید."

#: includes/widgets/icon.php:326
msgid "Fit to Size"
msgstr "متناسب با اندازه"

#: includes/widgets/accordion.php:154
msgid "You are currently editing an Accordion Widget in its old version. Any new Accordion widget dragged into the canvas will be the new Accordion widget, with the improved Nested capabilities."
msgstr "در حال حاضر در حال ویرایش یک ویجت آکاردئونی در نسخه قدیمی آن هستید. هر ویجت جدید آکاردئون که به داخل بوم کشیده شود، ویجت جدید آکاردئون خواهد بود، با قابلیت‌های تو در تو بهبودیافته."

#: core/document-types/page-base.php:257
msgid "Allow Comments"
msgstr "اجازه دادن به نظرات"

#: includes/frontend.php:1423
msgid "This is the last slide"
msgstr "این آخرین اسلاید است"

#: includes/frontend.php:1422
msgid "This is the first slide"
msgstr "این اولین اسلاید است"

#: includes/editor-templates/navigator.php:88
msgid "Show/hide Element"
msgstr "نمایش/پنهان کردن عنصر"

#: includes/editor-templates/navigator.php:75
msgid "Show/hide inner elements"
msgstr "نمایش/پنهان کردن عناصر درونی"

#: includes/editor-templates/navigator.php:64
msgid "Resize navigator"
msgstr "تغییر اندازه ناوبر"

#: includes/editor-templates/navigator.php:63
msgid "Resize structure"
msgstr "تغییر اندازه ساختار"

#: includes/controls/groups/background.php:99 assets/js/ai-admin.js:11264
#: assets/js/ai-gutenberg.js:13112 assets/js/ai-media-library.js:12893
#: assets/js/ai-unify-product-images.js:12893 assets/js/ai.js:14340
msgid "Gradient"
msgstr "گرادیان"

#: core/document-types/page-base.php:245
#: includes/controls/groups/flex-item.php:80
#: assets/js/packages/editor-editing-panel/editor-editing-panel.js:8
#: assets/js/packages/editor-editing-panel/editor-editing-panel.strings.js:108
msgid "Order"
msgstr "ترتیب"

#: includes/template-library/sources/local.php:515
msgid "Invalid template type."
msgstr "نوع قالب نامعتبر است."

#: core/admin/admin.php:347
msgid "Get Elementor Pro"
msgstr "دریافت المنتور پیشرفته"

#: includes/controls/base-units.php:130
msgid "Switch units"
msgstr "تغییر واحدها"

#: includes/editor-templates/global.php:9
msgid "Select your structure"
msgstr "ساختار خود را انتخاب کنید"

#: includes/editor-templates/global.php:52
msgid "Which layout would you like to use?"
msgstr "از کدام چیدمان می‌خواهید استفاده کنید؟"

#: core/kits/documents/tabs/settings-layout.php:100
#: includes/controls/groups/flex-container.php:156
#: includes/controls/groups/grid-container.php:96
#: includes/elements/container.php:495
#: assets/js/packages/editor-editing-panel/editor-editing-panel.js:8
#: assets/js/packages/editor-editing-panel/editor-editing-panel.strings.js:97
msgid "Gaps"
msgstr "شکاف‌ها"

#. translators: %s: Document title.
#: core/editor/loader/v1/templates/editor-body-v1-view.php:27
#: core/editor/loader/v2/templates/editor-body-v2-view.php:27
#: includes/editor-templates/editor-wrapper.php:30
#: assets/js/packages/editor-documents/editor-documents.js:2
#: assets/js/packages/editor-documents/editor-documents.strings.js:2
msgid "Edit \"%s\" with Elementor"
msgstr "ویرایش \"%s\" با المنتور"

#: modules/floating-buttons/base/widget-contact-button-base.php:116
msgid "Top Bar"
msgstr "نوار بالا"

#: includes/controls/gaps.php:58
#: includes/controls/groups/grid-container.php:118
#: assets/js/packages/editor-controls/editor-controls.js:10
#: assets/js/packages/editor-editing-panel/editor-editing-panel.js:8
#: assets/js/packages/editor-editing-panel/editor-editing-panel.strings.js:110
msgid "Row"
msgstr "ردیف"

#: includes/controls/groups/grid-container.php:71
msgid "Rows"
msgstr "ردیف‌ها"

#. translators: 1: Link open tag, 2: Link close tag.
#: core/document-types/page-base.php:100
msgid "Set a different selector for the title in the %1$sLayout panel%2$s."
msgstr "انتخابگر دیگری برای عنوان در %1$s پانل چیدمان%2$s تنظیم کنید."

#: includes/editor-templates/global.php:68 includes/elements/container.php:96
#: includes/elements/container.php:104 includes/elements/container.php:364
#: assets/js/editor.js:33558 assets/js/editor.js:42248
msgid "Grid"
msgstr "شبکه ای"

#: includes/editor-templates/global.php:60 includes/elements/container.php:363
msgid "Flexbox"
msgstr "فلکس باکس"

#. translators: 1: Link open tag, 2: Link close tag.
#: includes/widgets/image-carousel.php:370
#: includes/widgets/image-gallery.php:204 includes/widgets/image.php:229
msgid "Manage your site’s lightbox settings in the %1$sLightbox panel%2$s."
msgstr "تنظیمات لایت باکس سایت خود را در %1$sپنل جعبه نور%2$s مدیریت کنید."

#: includes/elements/container.php:358
msgid "Container Layout"
msgstr "چیدمان کانتینر"

#: includes/editor-templates/panel-elements.php:15
msgid "Globals"
msgstr "جهانی ها"

#: includes/editor-templates/navigator.php:39
msgid "Close navigator"
msgstr "ناوبر را ببندید"

#: includes/editor-templates/navigator.php:39
msgid "Close structure"
msgstr "ساختار بسته"

#: includes/controls/groups/grid-container.php:115
msgid "Auto Flow"
msgstr "جریان خودکار"

#: includes/controls/groups/grid-container.php:31
msgid "Grid Outline"
msgstr "طرح کلی شبکه"

#: modules/ai/connect/ai.php:27 assets/js/ai-admin.js:656
#: assets/js/ai-admin.js:7775 assets/js/ai-gutenberg.js:2424
#: assets/js/ai-gutenberg.js:9623 assets/js/ai-layout.js:488
#: assets/js/ai-layout.js:3256 assets/js/ai-media-library.js:2285
#: assets/js/ai-media-library.js:9404 assets/js/ai-unify-product-images.js:2285
#: assets/js/ai-unify-product-images.js:9404 assets/js/ai.js:3064
#: assets/js/ai.js:10851
msgid "AI"
msgstr "AI"

#: includes/editor-templates/navigator.php:35 assets/js/editor.js:35412
msgid "Expand all elements"
msgstr "باز کردن همه عناصر"

#: includes/widgets/icon-list.php:599
msgid "Adjust Vertical Position"
msgstr "تنظیم موقعیت عمودی"

#: includes/widgets/alert.php:506 includes/widgets/alert.php:561
msgid "Dismiss this alert."
msgstr "این هشدار را رد کنید."

#: modules/editor-app-bar/module.php:46
msgid "Editor Top Bar"
msgstr "نوار بالا ویرایشگر"

#: core/admin/admin-notices.php:580 core/admin/admin.php:1036
msgid "Dismiss this notice."
msgstr "این اطلاعیه را رد کنید."

#: core/breakpoints/manager.php:329
msgid "Tablet Landscape"
msgstr "حالت افقی تبلت"

#: core/breakpoints/manager.php:324
msgid "Tablet Portrait"
msgstr "حالت عمودی تبلت"

#: includes/elements/column.php:210 includes/widgets/icon-list.php:542
msgid "Horizontal Alignment"
msgstr "تراز افقی"

#: modules/editor-app-bar/module.php:49
msgid "Get a sneak peek of the new Editor powered by React. The beautiful design and experimental layout of the Top bar are just some of the exciting tools on their way."
msgstr "نگاهی دزدکی به ویرایشگر جدید که توسط React ارائه شده است را مشاهده کنید. طراحی زیبا و چیدمان آزمایشی نوار بالا تنها بخشی از ابزارهای هیجان انگیزی است که در راه است."

#: modules/generator-tag/module.php:76
msgid "Generator Tag"
msgstr "برچسب ژنراتور"

#: core/kits/documents/tabs/theme-style-form-fields.php:188
msgid "Accent Color"
msgstr "رنگ تاکیدی"

#: core/experiments/manager.php:546
msgid "Deactivate All"
msgstr "غیرفعال کردن همه"

#. translators: %1$s Link open tag, %2$s: Link close tag.
#: core/experiments/manager.php:528
msgid "Personalize your Elementor experience by controlling which features and experiments are active on your site. Help make Elementor better by %1$ssharing your experience and feedback with us%2$s."
msgstr "با کنترل اینکه کدام ویژگی‌ها و آزمایش‌ها در سایت شما فعال هستند، تجربه المنتور خود را شخصی کنید. با %1$s اشتراک گذاری تجربه و بازخورد خود با ما%2$s، به بهتر شدن المنتور کمک کنید."

#. translators: %d: Number of rows.
#: includes/utils.php:254
msgid "%d database row affected."
msgid_plural "%d database rows affected."
msgstr[0] "%d ردیف پایگاه داده تحت تأثیر قرار گرفت."

#: core/experiments/manager.php:545
msgid "Activate All"
msgstr "فعال کردن همه"

#: core/experiments/manager.php:522
msgid "Experiments and Features"
msgstr "آزمایش‌ها و ویژگی‌ها"

#: modules/generator-tag/module.php:84
msgid "A generator tag is a meta element that indicates the attributes used to create a webpage. It is used for analytical purposes."
msgstr "تگ ژنراتور یک عنصر متا است که ویژگی‌های مورد استفاده برای ایجاد یک صفحه وب را نشان می‌دهد. برای اهداف تحلیلی استفاده می‌شود."

#: modules/nested-tabs/widgets/nested-tabs.php:713
msgid "Titles"
msgstr "عنوان‌ها"

#: includes/widgets/video.php:853
msgid "Shadow"
msgstr "سایه"

#: modules/nested-accordion/widgets/nested-accordion.php:427
#: modules/nested-tabs/widgets/nested-tabs.php:469
msgid "Distance from content"
msgstr "فاصله از محتوا"

#: modules/nested-tabs/widgets/nested-tabs.php:178
msgid "Tab #3"
msgstr "زبانه ۳"

#: modules/nested-tabs/widgets/nested-tabs.php:80
msgid "Tab #%d"
msgstr "زبانه #%d"

#: modules/nested-tabs/widgets/nested-tabs.php:61
msgid "Tab #%s"
msgstr "زبانه #%s"

#. translators: 1: Breakpoint label, 2: `>` character, 3: Breakpoint value.
#: modules/nested-tabs/widgets/nested-tabs.php:422
msgid "%1$s (%2$s %3$dpx)"
msgstr "%1$s (%2$s %3$dpx)"

#: includes/settings/settings.php:365
#: assets/js/packages/editor-controls/editor-controls.js:10
msgid "Google Fonts"
msgstr "فونت‌های گوگل"

#: includes/widgets/video.php:574
msgid "Preload"
msgstr "پیش بارگذاری"

#: includes/widgets/tabs.php:153
msgid "You are currently editing a Tabs Widget in its old version. Any new tabs widget dragged into the canvas will be the new Tab widget, with the improved Nested capabilities."
msgstr "شما در حال حاضر در حال ویرایش یک ابزارک Tabs در نسخه قدیمی آن هستید. هر ویجت برگه‌های جدیدی که به داخل بوم کشیده شود، ویجت Tab جدید با قابلیت‌های Nested بهبودیافته خواهد بود."

#: modules/nested-elements/module.php:17
msgid "Nested Elements"
msgstr "عناصر تو در تو"

#: includes/widgets/video.php:577
msgid "Metadata"
msgstr "متا‌داده"

#: includes/settings/settings.php:374
msgid "Disable this option if you want to prevent Google Fonts from being loaded. This setting is recommended when loading fonts from a different source (plugin, theme or %1$scustom fonts%2$s)."
msgstr "اگر می‌خواهید از بارگذاری فونت‌های گوگل جلوگیری کنید، این گزینه را غیرفعال کنید. این تنظیم هنگام بارگذاری فونت‌ها از منبع دیگری (افزونه، پوسته یا %1$sقلم‌های سفارشی%2$s) توصیه می‌شود."

#: modules/nested-tabs/widgets/nested-tabs.php:434
msgid "Note: Choose at which breakpoint tabs will automatically switch to a vertical (“accordion”) layout."
msgstr "توجه: انتخاب کنید که در کدام نقطه شکست٬ طرح‌بندی تب‌ها به عمودی (\"آکاردیون\") عوض شود."

#: modules/nested-tabs/widgets/nested-tabs.php:449
msgid "Gap between tabs"
msgstr "شکاف بین زبانه‌ها"

#: includes/widgets/video.php:583
msgid "Preload attribute lets you specify how the video should be loaded when the page loads."
msgstr "ویژگی پیش‌بارگذاری به شما امکان می‌دهد مشخص کنید که هنگام بارگذاری صفحه چگونه ویدیو باید بارگذاری شود."

#: modules/nested-elements/module.php:20
msgid "Create a rich user experience by layering widgets together inside \"Nested\" Tabs, etc. When turned on, we’ll automatically enable new nested features. Your old widgets won’t be affected."
msgstr "با لایه‌بندی ویجت‌ها در داخل برگه‌های «تودرتو» و غیره، یک تجربه کاربری غنی ایجاد کنید. وقتی روشن است، ویژگی‌های تودرتو جدید را به‌طور خودکار فعال می‌کنیم. ویجت‌های قدیمی شما تحت تأثیر قرار نخواهند گرفت."

#: modules/nested-tabs/widgets/nested-tabs.php:342
msgid "Align Title"
msgstr "تراز عنوان"

#: includes/elements/container.php:1931
msgid "Note: Avoid applying transform properties on sticky containers. Doing so might cause unexpected results."
msgstr "توجه: از اعمال خواص تبدیل روی ظروف چسبناک خودداری کنید. انجام این کار ممکن است باعث نتایج غیرمنتظره شود."

#: includes/settings/settings.php:458
msgid "Lazy Load Background Images"
msgstr "بارگذاری تنبل تصاویر پس‌زمینه"

#: core/experiments/manager.php:618
msgid "Requires"
msgstr "ضروری است"

#: includes/controls/groups/typography.php:145
msgctxt "Typography Control"
msgid "(Normal)"
msgstr "(عادی)"

#: includes/controls/groups/typography.php:146
msgctxt "Typography Control"
msgid "(Medium)"
msgstr "(متوسط)"

#: includes/controls/groups/typography.php:147
msgctxt "Typography Control"
msgid "(Semi Bold)"
msgstr "(نیمه ضخیم)"

#: includes/controls/groups/typography.php:148
msgctxt "Typography Control"
msgid "(Bold)"
msgstr "(ضخیم)"

#: includes/controls/groups/typography.php:149
msgctxt "Typography Control"
msgid "(Extra Bold)"
msgstr "(بسیار ضخیم)"

#: includes/controls/groups/typography.php:142
msgctxt "Typography Control"
msgid "(Thin)"
msgstr "(باریک)"

#: includes/controls/groups/typography.php:143
msgctxt "Typography Control"
msgid "(Extra Light)"
msgstr "(خیلی نازک)"

#: includes/controls/groups/typography.php:144
msgctxt "Typography Control"
msgid "(Light)"
msgstr "(نازک)"

#: includes/controls/groups/typography.php:150
msgctxt "Typography Control"
msgid "(Black)"
msgstr "(پر)"

#: core/admin/admin.php:1024 modules/apps/admin-apps-page.php:116
#: modules/apps/admin-apps-page.php:147
#: modules/home/<USER>/filter-plugins.php:82 assets/js/admin.js:795
msgid "Activate"
msgstr "فعال"

#: includes/elements/container.php:564
msgid "(link)"
msgstr "(پیوند)"

#: app/modules/import-export/module.php:235
msgid "Remove Kit"
msgstr "حذف کیت"

#: app/modules/import-export/module.php:228
msgid "Remove the most recent Kit"
msgstr "جدیدترین کیت را حذف کنید"

#: app/modules/import-export/module.php:182
#: app/modules/import-export/module.php:185
#: app/modules/import-export/module.php:191
msgid "imported kit"
msgstr "کیت دورن‌ریزی شده"

#: app/modules/import-export/module.php:190
msgid "Remove all the content and site settings that came with \"%1$s\" on %2$s.%3$s Your original site settings will be restored."
msgstr "تمام محتوا و تنظیمات سایت همراه با \"%1$s\" در %2$s را حذف کنید.%3$s تنظیمات اصلی سایت شما بازیابی خواهد شد."

#: app/modules/import-export/module.php:181
msgid "Remove all the content and site settings that came with \"%1$s\" on %2$s %3$s and revert to the site setting that came with \"%4$s\" on %5$s."
msgstr "تمام محتوا و تنظیمات سایت همراه با \"%1$s\" در %2$s %3$s را حذف کنید و به تنظیمات سایتی که با \"%4$s\" در %5$s ارائه شده است، برگردید."

#: includes/widgets/image-carousel.php:282
msgid "Next Arrow Icon"
msgstr "آیکون فلش بعدی"

#: includes/widgets/image-carousel.php:227
msgid "Previous Arrow Icon"
msgstr "آیکون فلش قبلی"

#: includes/widgets/alert.php:394
#: modules/floating-buttons/base/widget-contact-button-base.php:2934
msgid "Horizontal Position"
msgstr "‫موقعیت افقی"

#: includes/widgets/alert.php:376
#: modules/floating-buttons/base/widget-contact-button-base.php:2988
#: modules/floating-buttons/base/widget-floating-bars-base.php:1433
msgid "Vertical Position"
msgstr "‫موقعیت عمودی"

#: includes/widgets/alert.php:161 includes/widgets/alert.php:342
msgid "Dismiss Icon"
msgstr "رد‌کردن آیکون"

#: core/settings/editor-preferences/model.php:230
msgid "All Posts"
msgstr "همه نوشته ها"

#: core/settings/editor-preferences/model.php:229
msgid "This Post"
msgstr "این نوشته"

#: core/common/modules/finder/categories/settings.php:64
#: core/experiments/manager.php:313 core/experiments/manager.php:370
#: core/experiments/manager.php:380 includes/settings/settings.php:400
#: includes/settings/settings.php:403 modules/element-cache/module.php:43
msgid "Performance"
msgstr "کارایی"

#: core/admin/admin-notices.php:368
msgid "Try it out"
msgstr "امتحانش کن"

#: core/kits/documents/tabs/settings-layout.php:88
msgid "Container Padding"
msgstr "فاصله کانتینر"

#. Translators: %s is the current item index.
#: modules/nested-accordion/widgets/nested-accordion.php:85
#: modules/nested-elements/base/widget-nested-base.php:45
#: assets/js/editor.js:25374
msgid "Item #%d"
msgstr "مورد #%d"

#: includes/editor-templates/panel.php:60
msgid "Any time you can change the settings in %1$sUser Preferences%2$s"
msgstr "هر زمان که می‌توانید تنظیمات را در %1$s تنظیمات کاربر%2$s تغییر دهید"

#: includes/editor-templates/panel.php:56
msgid "Now you can choose where you want to go on the site from the following options"
msgstr "اکنون می توانید از میان گزینه های زیر محل مورد نظر خود را در سایت انتخاب کنید"

#: includes/elements/container.php:585
msgid "Don’t add links to elements nested in this container - it will break the layout."
msgstr "به عناصر تو در تو در این کانتینر پیوند اضافه نکنید - چیدمان را خراب می کند."

#: core/admin/admin-notices.php:364
msgid "Improve your site’s performance score."
msgstr "امتیاز عملکرد سایت خود را بهبود بخشید."

#: core/admin/admin-notices.php:365
msgid "With our experimental speed boosting features you can go faster than ever before. Look for the Performance label on our Experiments page and activate those experiments to improve your site loading speed."
msgstr "با ویژگی‌های آزمایشی افزایش سرعت ما می‌توانید سریع‌تر از همیشه پیش بروید. به دنبال برچسب عملکرد در صفحه آزمایش‌های ما بگردید و آن آزمایش‌ها را برای بهبود سرعت بارگذاری سایت خود فعال کنید."

#: core/settings/editor-preferences/model.php:231
msgid "WP Dashboard"
msgstr "پیشخوان وردپرس"

#: core/kits/documents/tabs/settings-layout.php:91
msgid "Sets the default space inside the container (Default is 10px)"
msgstr "فضای پیش‌فرض داخل ظرف را تنظیم می‌کند (پیش‌فرض 10 پیکسل)"

#: core/settings/editor-preferences/model.php:225
msgid "Exit to"
msgstr "رفتن به"

#: app/modules/onboarding/module.php:151
msgid "There was a problem setting your site name."
msgstr "مشکلی در تنظیم نام سایت شما وجود دارد."

#: core/editor/notice-bar.php:45 assets/js/element-manager-admin.js:2352
msgid "Unleash the full power of Elementor's features and web creation tools."
msgstr "قدرت کامل ویژگی های المنتور و ابزارهای ایجاد وب را فعال کنید."

#: includes/editor-templates/hotkeys.php:191 assets/js/notes.js:136
#: assets/js/notes.js:140 assets/js/notes.js:226
msgid "Notes"
msgstr "یادداشت‌ها"

#: core/editor/notice-bar.php:41 core/editor/promotion.php:34
#: includes/editor-templates/navigator.php:20
#: includes/editor-templates/panel-elements.php:29
#: includes/editor-templates/panel-elements.php:34
#: includes/editor-templates/panel-elements.php:97
#: includes/managers/controls.php:1161 includes/widgets/image-gallery.php:110
#: modules/admin-top-bar/module.php:79
#: modules/checklist/steps/setup-header.php:93
#: modules/element-manager/ajax.php:73 modules/element-manager/ajax.php:80
#: modules/element-manager/ajax.php:88
#: modules/promotions/admin-menu-items/base-promotion-item.php:32
#: modules/promotions/admin-menu-items/base-promotion-template.php:37
#: modules/promotions/admin-menu-items/popups-promotion-item.php:24
#: modules/promotions/promotion-data.php:48
#: modules/promotions/promotion-data.php:65
#: modules/promotions/promotion-data.php:82
#: modules/promotions/promotion-data.php:99
#: modules/promotions/promotion-data.php:116 assets/js/app-packages.js:2413
#: assets/js/app-packages.js:5590 assets/js/app-packages.js:5856
#: assets/js/app.js:2727 assets/js/checklist.js:298 assets/js/editor.js:8255
#: assets/js/editor.js:52838 assets/js/editor.js:52853
#: assets/js/editor.js:52868 assets/js/editor.js:52883
#: assets/js/onboarding.120a44527e5a7209a8e4.bundle.js:513
#: assets/js/onboarding.120a44527e5a7209a8e4.bundle.js:1461
msgid "Upgrade Now"
msgstr "هم اکنون ارتقا دهید"

#: core/admin/admin.php:632 core/role-manager/role-manager.php:241
#: includes/editor-templates/panel-elements.php:40
#: includes/editor-templates/panel-elements.php:46
#: includes/editor-templates/panel-elements.php:64
#: includes/editor-templates/panel.php:328 includes/managers/controls.php:1152
#: includes/widgets/image-gallery.php:107
#: modules/promotions/admin-menu-items/go-pro-promotion-item.php:30
#: modules/promotions/promotion-data.php:41
#: modules/promotions/promotion-data.php:58
#: modules/promotions/promotion-data.php:75
#: modules/promotions/promotion-data.php:92
#: modules/promotions/promotion-data.php:109 assets/js/ai-admin.js:1024
#: assets/js/ai-admin.js:2966 assets/js/ai-admin.js:3095
#: assets/js/ai-gutenberg.js:2792 assets/js/ai-gutenberg.js:4734
#: assets/js/ai-gutenberg.js:4863 assets/js/ai-layout.js:722
#: assets/js/ai-layout.js:1002 assets/js/ai-media-library.js:2653
#: assets/js/ai-media-library.js:4595 assets/js/ai-media-library.js:4724
#: assets/js/ai-unify-product-images.js:2653
#: assets/js/ai-unify-product-images.js:4595
#: assets/js/ai-unify-product-images.js:4724 assets/js/ai.js:3432
#: assets/js/ai.js:5374 assets/js/ai.js:5503 assets/js/app-packages.js:5821
#: assets/js/e-react-promotions.js:196 assets/js/editor.js:6772
#: assets/js/kit-library.b0f0ab89c95fe1f6fde3.bundle.js:1320
#: assets/js/notes.js:149
#: assets/js/onboarding.120a44527e5a7209a8e4.bundle.js:767
#: assets/js/styleguide.js:196
msgid "Upgrade"
msgstr "ارتقا"

#: modules/announcements/module.php:116
msgid "Let's do it"
msgstr "بیا انجامش بدیم"

#: includes/elements/container.php:479
msgid "To achieve full height Container use %s."
msgstr "برای دستیابی به کانتینر با ارتفاع کامل، از %s استفاده کنید."

#: core/common/modules/finder/categories/tools.php:68
msgid "Import Export"
msgstr "درون ریزی برون بری"

#: includes/controls/groups/typography.php:153
msgid "Bold"
msgstr "برجسته"

#: includes/widgets/image-carousel.php:624
msgid "Pagination"
msgstr "صفحه بندی"

#: core/kits/documents/tabs/global-colors.php:101
#: assets/js/styleguide-app.a6e297c616479b98c03d.bundle.js:178
msgid "System Colors"
msgstr "رنگ‌های سیستم"

#: modules/container-converter/module.php:86
#: modules/container-converter/module.php:119
msgid "Convert"
msgstr "تبدیل"

#: includes/elements/column.php:453 includes/elements/section.php:728
#: includes/widgets/heading.php:321
msgid "Difference"
msgstr "تفاوت"

#: includes/elements/column.php:454 includes/elements/section.php:729
#: includes/widgets/heading.php:322
msgid "Exclusion"
msgstr "استثنا"

#: core/kits/views/panel.php:36
msgid "Reorder"
msgstr "مرتب‌سازی دوباره"

#: core/kits/documents/tabs/global-typography.php:160
#: assets/js/styleguide-app.a6e297c616479b98c03d.bundle.js:227
msgid "System Fonts"
msgstr "فونت‌های سیستم"

#: modules/container-converter/module.php:85
#: modules/container-converter/module.php:118
msgid "Convert to container"
msgstr "تبدیل به کانتینر"

#: elementor.php:78 elementor.php:102 assets/js/ai-admin.js:1063
#: assets/js/ai-gutenberg.js:2831 assets/js/ai-layout.js:761
#: assets/js/ai-media-library.js:2692 assets/js/ai-unify-product-images.js:2692
#: assets/js/ai.js:3471
msgid "Show me how"
msgstr "نمایش بده چطوری میشه!"

#: includes/editor-templates/global.php:33 assets/js/editor.js:33417
msgid "Add New Container"
msgstr "افزودن کانتینر جدید"

#: core/experiments/manager.php:342 includes/elements/container.php:72
#: includes/elements/container.php:342
#: modules/library/documents/container.php:52 assets/js/editor.js:10294
#: assets/js/editor.js:33558 assets/js/editor.js:39136
msgid "Container"
msgstr "کانتینر"

#: includes/widgets/video.php:989
msgid "Play Video about"
msgstr "نمایش ویدیو درباره"

#: modules/container-converter/module.php:88
#: modules/container-converter/module.php:121
msgid "Copies all of the selected sections and columns and pastes them in a container beneath the original."
msgstr "تمام بخش‌ها و ستون‌های انتخاب شده را کپی می‌کند و آن‌ها را در ظرفی زیر نسخه اصلی قرار می‌دهد."

#: includes/elements/column.php:455 includes/elements/section.php:730
#: includes/widgets/heading.php:323
msgid "Hue"
msgstr "رنگ"

#: includes/controls/groups/background.php:727
#: includes/widgets/image-carousel.php:423
msgid "Lazyload"
msgstr "بارگذاری تنبل"

#: core/common/modules/event-tracker/personal-data.php:24
msgid "Elementor Event Tracker"
msgstr "ردیاب رویداد المنتور"

#: app/modules/onboarding/module.php:258 app/modules/onboarding/module.php:343
msgid "There was a problem uploading your file."
msgstr "مشکلی در آپلود فایل شما وجود دارد."

#: app/modules/onboarding/module.php:208
msgid "There was a problem setting your site logo."
msgstr "مشکلی در تنظیم لوگوی سایت شما وجود دارد."

#: modules/library/documents/page.php:65
msgid "Add New Page Template"
msgstr "افزودن قالب برگه جدید"

#: includes/admin-templates/beta-tester.php:53 assets/js/ai-admin.js:6389
#: assets/js/ai-admin.js:15850 assets/js/ai-gutenberg.js:8237
#: assets/js/ai-gutenberg.js:17698 assets/js/ai-layout.js:2482
#: assets/js/ai-layout.js:5200 assets/js/ai-media-library.js:8018
#: assets/js/ai-media-library.js:17479
#: assets/js/ai-unify-product-images.js:8018
#: assets/js/ai-unify-product-images.js:17479 assets/js/ai.js:9362
#: assets/js/ai.js:9465 assets/js/ai.js:18926
msgid "Privacy Policy"
msgstr "سیاست حفظ حریم خصوصی"

#: includes/admin-templates/beta-tester.php:48 assets/js/ai-admin.js:6385
#: assets/js/ai-admin.js:15846 assets/js/ai-gutenberg.js:8233
#: assets/js/ai-gutenberg.js:17694 assets/js/ai-layout.js:2478
#: assets/js/ai-layout.js:5196 assets/js/ai-media-library.js:8014
#: assets/js/ai-media-library.js:17475
#: assets/js/ai-unify-product-images.js:8014
#: assets/js/ai-unify-product-images.js:17475 assets/js/ai.js:9358
#: assets/js/ai.js:9461 assets/js/ai.js:18922
msgid "Terms of Service"
msgstr "شرایط استفاده از خدمات"

#. translators: 1. "Terms of service" link, 2. "Privacy policy" link
#: includes/admin-templates/beta-tester.php:44
msgid "By clicking Sign Up, you agree to Elementor's %1$s and %2$s"
msgstr "با کلیک بر روی ثبت‌نام، با %1$s و %2$s المنتور موافقت می‌کنید"

#: core/editor/promotion.php:31 assets/js/e-react-promotions.js:196
#: assets/js/editor.js:6772 assets/js/notes.js:149 assets/js/styleguide.js:196
msgid "Connect & Activate"
msgstr "اتصال و فعال‌سازی"

#: includes/controls/groups/text-stroke.php:85
#: assets/js/packages/editor-controls/editor-controls.js:8
msgid "Stroke Color"
msgstr "رنگ حاشیه"

#: core/experiments/manager.php:551
msgid "Ongoing Experiments"
msgstr "آزمایشات در حال انجام"

#: core/experiments/manager.php:508
msgid "Stable Features"
msgstr "امکانات باثبات"

#: includes/base/element-base.php:977
msgid "Perspective"
msgstr "پرسپکتیو"

#: includes/base/element-base.php:1191
msgid "Skew Y"
msgstr "خمیدگی محور Y"

#: includes/base/element-base.php:1169
msgid "Skew X"
msgstr "خمیدگی محور X"

#: includes/base/element-base.php:1157
msgid "Skew"
msgstr "خمیدگی"

#: includes/base/element-base.php:1078
msgid "Keep Proportions"
msgstr "حفظ تناسب"

#: includes/base/element-base.php:1038
msgid "Offset Y"
msgstr "انحراف محور Y"

#: includes/base/element-base.php:1012
msgid "Offset X"
msgstr "انحراف محور X"

#: includes/base/element-base.php:954
msgid "Rotate Y"
msgstr "چرخش y"

#: includes/base/element-base.php:931
msgid "Rotate X"
msgstr "چرخش X"

#: includes/base/element-base.php:914
msgid "3D Rotate"
msgstr "چرخش سه‌بعدی"

#: modules/promotions/admin-menu-items/custom-code-promotion-item.php:15
#: modules/promotions/admin-menu-items/custom-code-promotion-item.php:19
msgid "Custom Code"
msgstr "کد سفارشی"

#: includes/base/element-base.php:1234 includes/base/element-base.php:1238
msgid "Flip Vertical"
msgstr "برگردان عمودی"

#: includes/base/element-base.php:1215 includes/base/element-base.php:1219
msgid "Flip Horizontal"
msgstr "برگردان افقی"

#: modules/usage/usage-reporter.php:22
msgid "Elements Usage"
msgstr "استفاده از عناصر"

#: includes/base/element-base.php:851
#: assets/js/packages/editor-editing-panel/editor-editing-panel.js:8
#: assets/js/packages/editor-editing-panel/editor-editing-panel.strings.js:40
msgid "Transform"
msgstr "تبدیل"

#: includes/controls/groups/text-stroke.php:60
#: includes/controls/groups/text-stroke.php:111
#: assets/js/packages/editor-editing-panel/editor-editing-panel.js:8
#: assets/js/packages/editor-editing-panel/editor-editing-panel.strings.js:42
msgid "Text Stroke"
msgstr "مرز متن"

#: core/logger/log-reporter.php:25
msgid "Log"
msgstr "گزارش"

#. translators: 1: Integration settings link open tag, 2: Create API key link
#. open tag, 3: Link close tag.
#: includes/widgets/google-maps.php:140
msgid "Set your Google Maps API Key in Elementor's %1$sIntegrations Settings%3$s page. Create your key %2$shere.%3$s"
msgstr "کلید API گوگل مپ را در صفحه %1$sتنظیمات یکپارچه سازی%3$s تنظیم کنید. میتونید از %2$sاینجا%3$s کلید api بسازید"

#: includes/base/element-base.php:1133
msgid "Scale Y"
msgstr "محور Y"

#: includes/base/element-base.php:1111
msgid "Scale X"
msgstr "محور X"

#: includes/base/element-base.php:1336
msgid "Y Anchor Point"
msgstr "Y Anchor Point"

#: includes/base/element-base.php:1308
msgid "X Anchor Point"
msgstr "X Anchor Point"

#: core/editor/data/globals/endpoints/base.php:46
msgid "Invalid title"
msgstr "عنوان نامعتبر"

#: core/experiments/experiments-reporter.php:21
msgid "Elementor Experiments"
msgstr "آزمایشات عنصر"

#: core/experiments/manager.php:312
msgid "Inline Font Icons"
msgstr "نمادهای فونت درون خطی"

#: core/experiments/manager.php:316
msgid "The “Inline Font Icons” will render the icons as inline SVG without loading the Font-Awesome and the eicons libraries and its related CSS files and fonts."
msgstr "\"فونت آیکون‌های توکار\" نمادها را بدون بارگیری Font-Awesome و کتابخانه‌های eicons و فایل‌ها و فونت‌های CSS مربوط به آن، به‌صورت SVG توکار ارائه می‌کند."

#: includes/settings/tools.php:158
msgid "Not allowed to rollback versions"
msgstr "نه مجاز به عقب برگشتن نسخه ها"

#: modules/page-templates/module.php:316
msgid "The default page template as defined in Elementor Panel → Hamburger Menu → Site Settings."
msgstr "قالب صفحه پیش‌فرض در پنل المنتور ← منوی همبرگری ← تنظیمات سایت تعریف شده است."

#: includes/elements/column.php:447 includes/elements/container.php:848
#: includes/elements/section.php:721 includes/widgets/heading.php:315
#: assets/js/packages/editor-controls/editor-controls.js:10
msgid "Overlay"
msgstr "روکش"

#: includes/elements/column.php:446 includes/elements/container.php:847
#: includes/elements/section.php:720 includes/widgets/heading.php:314
msgid "Screen"
msgstr "صفحه"

#: includes/elements/column.php:445 includes/elements/container.php:846
#: includes/elements/section.php:719 includes/widgets/heading.php:313
msgid "Multiply"
msgstr "ضرب"

#: includes/elements/column.php:450 includes/elements/container.php:851
#: includes/elements/section.php:724 includes/widgets/heading.php:318
msgid "Color Dodge"
msgstr "رنگ دوج"

#: includes/elements/column.php:456 includes/elements/container.php:854
#: includes/elements/section.php:727 includes/widgets/heading.php:324
msgid "Luminosity"
msgstr "درخشش"

#. translators: %1$s Link open tag, %2$s: Link close tag.
#: modules/safe-mode/module.php:290
msgid "Please note! We couldn't deactivate all of your plugins on Safe Mode. Please %1$sread more%2$s about this issue"
msgstr "لطفا توجه کنید! ما نمیتوانیم همه افزونه های شمارا در حالت امن غیرفعال کنید. لطفا %1$s درباره این مشکل %2$s بیشتر بخوانید."

#. translators: %1$s Link open tag, %2$s: Link close tag.
#: modules/safe-mode/module.php:261 modules/safe-mode/module.php:274
msgid "%1$sClick here%2$s to troubleshoot"
msgstr "%1$s برای عیب یابی اینجا %2$s کلیک کنید"

#: includes/elements/column.php:449 includes/elements/container.php:850
#: includes/elements/section.php:723 includes/widgets/heading.php:317
msgid "Lighten"
msgstr "خیلی روشن"

#: includes/elements/column.php:448 includes/elements/container.php:849
#: includes/elements/section.php:722 includes/widgets/heading.php:316
msgid "Darken"
msgstr "خیلی تیره"

#: includes/elements/column.php:451 includes/elements/container.php:852
#: includes/elements/section.php:725 includes/widgets/heading.php:319
msgid "Saturation"
msgstr "اشباع رنگی"

#. translators: %s: Device name.
#: includes/base/element-base.php:1383
msgid "Hide On %s"
msgstr "مخفی سازی در %s"

#: core/experiments/manager.php:329
msgid "Additional Custom Breakpoints"
msgstr "نقاط قطع سفارشی اضافی (جهت رسپانسیو سایت به کار میره)"

#. translators: %1$s Link open tag, %2$s: Link close tag.
#: includes/editor-templates/panel.php:213
msgid "You can enable it from the %1$sElementor settings page%2$s."
msgstr "شما میتوانید این را در%1$s تنظیمات المنتور %2$s.فعال کنید."

#. translators: %1$s Span open tag, %2$s: Span close tag.
#: includes/admin-templates/new-template.php:52
msgid "Templates Help You %1$sWork Efficiently%2$s"
msgstr "قالب‌ها به شما کمک می‌کنند تا %2$sکارآمدتر%1$s ویرایش کنید"

#. translators: %1$s Link open tag, %2$s: Link close tag.
#: modules/floating-buttons/module.php:383
msgid "Or view %1$sTrashed Items%1$s"
msgstr "یا %1$sموارد حذف شده%1$s را ببینید"

#. translators: %d: Breakpoint screen size.
#: includes/editor-templates/responsive-bar.php:26
msgid "Widescreen <br> Settings added for the Widescreen device will apply to screen sizes %dpx and up"
msgstr "صفحه عریض <br> تنظیمات اضافه شده برای دستگاه صفحه عریض در اندازه صفحه نمایش %dpx و بالاتر اعمال می‌شود"

#: core/kits/documents/tabs/settings-layout.php:371
msgid "Widescreen breakpoint settings will apply from the selected value and up."
msgstr "تنظیمات نقطه شکست عریض از مقدار انتخاب شده به بالا اعمال می‌شود."

#: includes/managers/controls.php:1138
msgid "Meet Page Transitions"
msgstr "‫با انتقال‌های صفحه آشنا شوید"

#: includes/managers/controls.php:1126
msgid "Page Transitions let you style entrance and exit animations between pages as well as display loader until your page assets load."
msgstr "انتقال‌های صفحه به شما امکان می‌دهد انیمیشن‌های ورود و خروج بین صفحات و همچنین نمایش دریافت را تنظیم کنید تا زمانی که محتوای صفحه شما دریافت شوند."

#: core/kits/documents/tabs/settings-page-transitions.php:19
#: includes/managers/controls.php:1120
msgid "Page Transitions"
msgstr "انتقال‌های صفحه"

#. translators: %1$s Link open tag, %2$s: Link close tag.
#: modules/history/views/revisions-panel-template.php:31
msgid "Learn more about %1$sWordPress revisions%2$s"
msgstr "درباره %1$sبازبینی‌های وردپرس%2$s بیشتر بیاموزید"

#. translators: %1$s Link open tag, %2$s: Link close tag.
#: includes/maintenance-mode.php:375
msgid "Select one or go ahead and %1$screate one%2$s now."
msgstr "یکی را انتخاب کنید یا ادامه دهید و یکی %1$sایجاد کنید%2$s."

#. translators: 1: Function argument, 2: Elementor version number.
#: modules/dev-tools/deprecation.php:292
msgid "The %1$s argument is deprecated since version %2$s!"
msgstr "آرگومنت %1$s از نسخه %2$s منسوخ شده است!"

#. translators: 1: Function argument, 2: Elementor version number, 3:
#. Replacement argument name.
#: modules/dev-tools/deprecation.php:288
msgid "The %1$s argument is deprecated since version %2$s! Use %3$s instead."
msgstr "آرگومنت %1$s از نسخه %2$s منسوخ شده است! به جای آن از %3$s استفاده کنید."

#. translators: 1: Link open tag, 2: Link close tag.
#: core/kits/documents/tabs/tab-base.php:80
msgid "In order for Theme Style to affect all relevant Elementor elements, please disable Default Colors and Fonts from the %1$sSettings Page%2$s."
msgstr "برای اینکه سبک قالب بر تمام عناصر مرتبط المنتور تأثیر بگذارد، لطفاً رنگ‌ها و قلم‌های پیش‌فرض را از %1$sصفحه تنظیمات%2$s غیرفعال کنید."

#: core/experiments/manager.php:332
msgid "Get pixel-perfect design for every screen size. You can now add up to 6 customizable breakpoints beyond the default desktop setting: mobile, mobile extra, tablet, tablet extra, laptop, and widescreen."
msgstr "برای هر اندازه صفحه‌نمایش، طراحی کامل پیکسلی دریافت کنید. اکنون می‌توانید تا ۶ نقطه شکست قابل تنظیم، فراتر از تنظیمات پیش‌فرض دسک‌تاپ اضافه کنید: تلفن همراه، تلفن همراه بزرگ، تبلت، تبلت بزرگ، لپ‌تاپ و نمایشگر عریض."

#. translators: 1: Link open tag, 2: Link close tag.
#: includes/settings/tools.php:403
msgid "%1$sClick here%2$s %3$sto join our first-to-know email updates.%4$s"
msgstr "%1$sاینجا کلیک کنید%2$s %3$sبرای پیوستن به به‌روزرسانی‌های ایمیلی \"برای اولین بار بدانید\" ما.%4$s"

#: includes/managers/elements.php:328
#: assets/js/kit-library.b0f0ab89c95fe1f6fde3.bundle.js:3662
msgid "Favorites"
msgstr "علاقه‌مندی‌ها"

#: core/common/modules/finder/categories/settings.php:74
#: core/experiments/manager.php:485
#: assets/js/kit-library.b0f0ab89c95fe1f6fde3.bundle.js:3286
msgid "Features"
msgstr "ویژگی‌ها"

#: includes/template-library/sources/admin-menu-items/templates-categories-menu-item.php:23
#: assets/js/kit-library.b0f0ab89c95fe1f6fde3.bundle.js:3277
msgid "Categories"
msgstr "دسته‌بندی‌ها"

#: modules/nested-accordion/widgets/nested-accordion.php:554
#: assets/js/app-packages.js:5346
msgid "Header"
msgstr "سربرگ"

#: modules/library/documents/section.php:47
msgid "Sections"
msgstr "بخش ها"

#: includes/settings/tools.php:431 includes/settings/tools.php:434
#: assets/js/editor.js:28990
msgid "Recreate Kit"
msgstr "ساخت مجدد کیت"

#: modules/promotions/admin-menu-items/form-submissions-promotion-item.php:15
#: modules/promotions/admin-menu-items/form-submissions-promotion-item.php:19
msgid "Submissions"
msgstr "ارسالی ها"

#: includes/settings/tools.php:107
msgid "An error occurred while trying to create a kit."
msgstr "یک خطای غیرمنتظره در هنگام ایجاد کیت رخ داده."

#: includes/settings/tools.php:112
msgid "New kit have been created successfully"
msgstr "کیت جدید با موفقیت ساخته شد"

#: includes/settings/tools.php:101
msgid "There's already an active kit."
msgstr "یک کیت فعال از قبل وجود دارد."

#: includes/editor-templates/panel.php:302 assets/js/editor.js:13939
msgid "Color Sampler"
msgstr "نمونه‌گیر رنگ"

#: core/settings/editor-preferences/model.php:119
msgid "Default device view"
msgstr "نمای دستگاه پیش‌فرض"

#: includes/settings/tools.php:435
msgid "It seems like your site doesn't have any active Kit. The active Kit includes all of your Site Settings. By recreating your Kit you will able to start edit your Site Settings again."
msgstr "به نظر می‌رسد سایت شما هیچ کیت فعالی ندارد. کیت فعال شامل تمام تنظیمات سایت شما می‌شود. با ایجاد مجدد کیت خود، می‌توانید دوباره تنظیمات سایت خود را ویرایش کنید."

#: app/modules/kit-library/data/repository.php:147
#: app/modules/kit-library/data/repository.php:167
msgid "Kit not found"
msgstr "کیت وجود ندارد"

#: app/modules/kit-library/connect/kit-library.php:16
#: app/modules/kit-library/kit-library-menu-item.php:22
#: app/modules/kit-library/module.php:34 app/modules/kit-library/module.php:35
#: core/common/modules/finder/categories/general.php:78
#: assets/js/import-export-admin.js:300
#: assets/js/kit-library.b0f0ab89c95fe1f6fde3.bundle.js:1538
#: assets/js/kit-library.b0f0ab89c95fe1f6fde3.bundle.js:3715
#: assets/js/kit-library.b0f0ab89c95fe1f6fde3.bundle.js:4153
#: assets/js/kit-library.b0f0ab89c95fe1f6fde3.bundle.js:4453
msgid "Kit Library"
msgstr "کتابخانه کیت"

#: app/modules/kit-library/data/kits/controller.php:29
msgid "Kit not exists."
msgstr "کیت وجود ندارد"

#: modules/global-classes/global-classes-rest-api.php:177
#: assets/js/packages/editor-global-classes/editor-global-classes.js:2
msgid "Something went wrong"
msgstr "مشکلی پیش آمده"

#: includes/settings/tools.php:341 assets/js/app.js:7614 assets/js/app.js:8335
msgid "Important:"
msgstr "مهم:"

#: modules/compatibility-tag/compatibility-tag-report.php:169
msgid "Compatible"
msgstr "سازگار"

#: modules/compatibility-tag/compatibility-tag-report.php:172
msgid "Compatibility unknown"
msgstr "سازگاری ناشناخته است"

#: includes/settings/settings.php:307
msgid "Google Maps Embed API"
msgstr "قراردادن API نقشه گوگل"

#: includes/settings/settings.php:318
msgid "API Key"
msgstr "کلید API"

#: modules/compatibility-tag/compatibility-tag-report.php:170
msgid "Incompatible"
msgstr "ناسازگار"

#: modules/compatibility-tag/compatibility-tag-report.php:171
msgid "Compatibility not specified"
msgstr "هیچ سازگاری تعیین نشده"

#. translators: 1: Link open tag, 2: Link close tag
#: includes/settings/settings.php:311
msgid "Google Maps Embed API is a free service by Google that allows embedding Google Maps in your site. For more details, visit Google Maps' %1$sUsing API Keys%2$s page."
msgstr "نقشه گوگل Embed API یک سرویس رایگان توسط گوگل است که امکان جاسازی نقشه های گوگل در سایت شما را فراهم می کند. برای جزئیات بیشتر، از صفحه %1$sاستفاده از کلیدهای API%2$s در نقشه گوگل دیدن کنید"

#: core/breakpoints/manager.php:339
msgid "Widescreen"
msgstr "صفحه عریض (Widescreen)"

#: includes/controls/groups/background.php:276
#: includes/controls/groups/background.php:328
#: includes/controls/groups/background.php:716
#: includes/widgets/common-base.php:1102 includes/widgets/image.php:409
#: modules/link-in-bio/base/widget-link-in-bio-base.php:188
#: assets/js/packages/editor-controls/editor-controls.js:10
#: assets/js/packages/editor-editing-panel/editor-editing-panel.js:8
#: assets/js/packages/editor-editing-panel/editor-editing-panel.strings.js:147
msgid "Bottom Right"
msgstr "پایین راست"

#: includes/controls/groups/background.php:274
#: includes/controls/groups/background.php:326
#: includes/controls/groups/background.php:714
#: includes/widgets/common-base.php:1100 includes/widgets/image.php:407
#: modules/link-in-bio/base/widget-link-in-bio-base.php:186
#: assets/js/packages/editor-controls/editor-controls.js:10
msgid "Bottom Center"
msgstr "پایین وسط"

#: includes/controls/groups/background.php:275
#: includes/controls/groups/background.php:327
#: includes/controls/groups/background.php:715
#: includes/widgets/common-base.php:1101 includes/widgets/image.php:408
#: modules/link-in-bio/base/widget-link-in-bio-base.php:187
#: assets/js/packages/editor-controls/editor-controls.js:10
#: assets/js/packages/editor-editing-panel/editor-editing-panel.js:8
#: assets/js/packages/editor-editing-panel/editor-editing-panel.strings.js:148
msgid "Bottom Left"
msgstr "پایین چپ"

#: includes/controls/groups/background.php:270
#: includes/controls/groups/background.php:322
#: includes/controls/groups/background.php:710
#: includes/widgets/common-base.php:1096 includes/widgets/image.php:403
#: modules/link-in-bio/base/widget-link-in-bio-base.php:182
#: assets/js/packages/editor-controls/editor-controls.js:10
msgid "Center Right"
msgstr "وسط راست"

#: includes/controls/groups/background.php:273
#: includes/controls/groups/background.php:325
#: includes/controls/groups/background.php:713
#: includes/widgets/common-base.php:1099 includes/widgets/image.php:406
#: modules/link-in-bio/base/widget-link-in-bio-base.php:185
#: assets/js/packages/editor-controls/editor-controls.js:10
#: assets/js/packages/editor-editing-panel/editor-editing-panel.js:8
#: assets/js/packages/editor-editing-panel/editor-editing-panel.strings.js:146
msgid "Top Right"
msgstr "بالا راست"

#: includes/controls/groups/background.php:272
#: includes/controls/groups/background.php:324
#: includes/controls/groups/background.php:712
#: includes/widgets/common-base.php:1098 includes/widgets/image.php:405
#: modules/link-in-bio/base/widget-link-in-bio-base.php:184
#: assets/js/packages/editor-controls/editor-controls.js:10
#: assets/js/packages/editor-editing-panel/editor-editing-panel.js:8
#: assets/js/packages/editor-editing-panel/editor-editing-panel.strings.js:145
msgid "Top Left"
msgstr "بالا چپ"

#: includes/controls/groups/background.php:271
#: includes/controls/groups/background.php:323
#: includes/controls/groups/background.php:711
#: includes/widgets/common-base.php:1097 includes/widgets/image.php:404
#: modules/link-in-bio/base/widget-link-in-bio-base.php:183
#: assets/js/packages/editor-controls/editor-controls.js:10
msgid "Top Center"
msgstr "بالا وسط"

#: includes/controls/groups/background.php:269
#: includes/controls/groups/background.php:321
#: includes/controls/groups/background.php:709
#: includes/widgets/common-base.php:1095 includes/widgets/image.php:402
#: modules/link-in-bio/base/widget-link-in-bio-base.php:181
#: assets/js/packages/editor-controls/editor-controls.js:10
msgid "Center Left"
msgstr "وسط چپ"

#: includes/controls/groups/background.php:268
#: includes/controls/groups/background.php:320
#: includes/controls/groups/background.php:708
#: includes/widgets/common-base.php:1094 includes/widgets/image.php:401
#: modules/link-in-bio/base/widget-link-in-bio-base.php:180
#: assets/js/packages/editor-controls/editor-controls.js:10
msgid "Center Center"
msgstr "وسط وسط"

#: includes/base/element-base.php:1066 includes/base/element-base.php:1089
#: includes/widgets/common-base.php:1059
msgid "Scale"
msgstr "مقیاس"

#: includes/settings/settings.php:390
msgid "Optional"
msgstr "اختیاری"

#: includes/widgets/common-base.php:1195
#: modules/floating-buttons/base/widget-contact-button-base.php:2083
#: modules/floating-buttons/base/widget-contact-button-base.php:2174
#: modules/floating-buttons/base/widget-contact-button-base.php:2867
#: modules/floating-buttons/base/widget-floating-bars-base.php:844
#: modules/link-in-bio/base/widget-link-in-bio-base.php:118
msgid "Round"
msgstr "گرد"

#: includes/widgets/common-base.php:1024
msgid "Need More Shapes?"
msgstr "به شکل‌های بیشتری نیاز دارید؟"

#: includes/widgets/common-base.php:136
msgid "Flower"
msgstr "گل"

#: includes/widgets/common-base.php:138
msgid "Triangle"
msgstr "مثلث"

#: includes/widgets/common-base.php:140
msgid "Hexagon"
msgstr "شش ضلعی"

#: includes/controls/groups/background.php:464
#: includes/widgets/common-base.php:1188 includes/widgets/common-base.php:1192
#: assets/js/packages/editor-controls/editor-controls.js:10
msgid "Repeat"
msgstr "تکرار"

#: includes/controls/groups/background.php:463
#: includes/widgets/common-base.php:1191
msgid "No-repeat"
msgstr "بدون تکرار"

#: includes/controls/groups/background.php:385
#: includes/widgets/common-base.php:1152
msgid "Y Position"
msgstr "موقعیت عمودی"

#: includes/controls/groups/background.php:342
#: includes/widgets/common-base.php:1116
msgid "X Position"
msgstr "موقعیت افقی"

#: includes/widgets/common-base.php:1044
msgid "Fit"
msgstr "هم اندازه"

#: includes/widgets/common-base.php:137
msgid "Sketch"
msgstr "طراحی اسکیس"

#: includes/widgets/accordion.php:262 includes/widgets/toggle.php:265
#: modules/nested-accordion/widgets/nested-accordion.php:307
msgid "FAQ Schema"
msgstr "طرح سوالات متداول"

#: includes/editor-templates/responsive-bar.php:62
msgid "Manage Breakpoints"
msgstr "مدیریت نقاط شکست"

#: core/admin/admin-notices.php:328
msgid "With Elementor Pro, you can control user access and make sure no one messes up your design."
msgstr "با المنتور پرو میتوانید دسترسی کاربر را کنترل کنید و مطمئن شوید کسی طرح شما را خراب نمی کند."

#: core/admin/admin-notices.php:327
msgid "Managing a multi-user site?"
msgstr "مدیریت سایتی چندکاربره؟"

#: includes/settings/settings.php:381
msgid "Google Fonts Load"
msgstr "فراخوانی فونت‌های گوگل"

#: includes/widgets/common-base.php:971 includes/widgets/common-base.php:979
msgid "Mask"
msgstr "ماسک"

#: includes/widgets/common-base.php:139
msgid "Blob"
msgstr "حباب"

#: includes/settings/settings.php:392
msgid "Font-display property defines how font files are loaded and displayed by the browser."
msgstr "ویژگی font-display تعیین میکند که فونت چگونه در مرورگر شما بارگذاری و به نمایش دربیاید."

#: core/base/db-upgrades-manager.php:114
msgid "Database update process is running in the background. Taking a while?"
msgstr "پردازش بروزرسانی دیتابیس در پس زمینه درحال اجرا است. مدتی طول میکشد؟"

#: includes/editor-templates/responsive-bar.php:22
msgid "Desktop <br> Settings added for the base device will apply to all breakpoints unless edited"
msgstr "دسکتاپ <br> تنظیمات اضافه شده برای دستگاه پایه برای همه نقاط شکست اعمال می شود مگر اینکه ویرایش شوند"

#. translators: %1$s: Device name, %2$s: Breakpoint screen size.
#: includes/editor-templates/responsive-bar.php:32
msgid "%1$s <br> Settings added for the %1$s device will apply to %2$spx screens and down"
msgstr "%1$s <br> تنظیمات اضافه‌شده برای دستگاه %1$s روی صفحه‌های %2$spx و پایین اعمال می‌شود"

#: includes/controls/groups/background.php:465
#: includes/widgets/common-base.php:1193
#: assets/js/packages/editor-controls/editor-controls.js:10
msgid "Repeat-x"
msgstr "تکرار-x"

#: includes/controls/groups/background.php:466
#: includes/widgets/common-base.php:1194
#: assets/js/packages/editor-controls/editor-controls.js:10
msgid "Repeat-y"
msgstr "تکرار-y"

#: includes/settings/settings.php:388
msgid "Swap"
msgstr "Swap"

#: includes/settings/settings.php:387
msgid "Blocking"
msgstr "Blocking"

#: includes/settings/settings.php:392
msgid "Set the way Google Fonts are being loaded by selecting the font-display property (Recommended: Swap)."
msgstr "با انتخاب ویژگی font-display نحوه بارگیری فونت‌های گوگل را تنظیم کنید (پیشنهاد می‌شود: Swap)."

#: core/admin/admin-notices.php:236
msgid "Love using Elementor?"
msgstr "استفاده از المنتور را دوست دارید؟"

#: app/modules/import-export/module.php:119
msgid "Template Kits"
msgstr "کیت های قالب"

#: app/modules/import-export/module.php:116
msgid "Import / Export Kit"
msgstr "درون ریزی / برون بری کیت"

#: app/modules/import-export/module.php:161
msgid "Apply the design and settings of another site to this one."
msgstr "طراحی و تنظیمات سایت دیگری را در این سایت اعمال کنید."

#: app/modules/import-export/module.php:159
msgid "Start Import"
msgstr "شروع درون‌ریزی"

#: app/modules/import-export/module.php:156
msgid "Import a Template Kit"
msgstr "درون‌ریزی کیت قالب"

#: app/modules/import-export/module.php:149
msgid "Bundle your whole site - or just some of its elements - to be used for another website."
msgstr "کل سایت خود را - یا فقط برخی از عناصر آن - را برای استفاده از وب سایت دیگر بسته‌بندی کنید."

#: app/modules/import-export/module.php:147
msgid "Start Export"
msgstr "شروع برون‌بری"

#: app/modules/import-export/module.php:144
msgid "Export a Template Kit"
msgstr "کیت قالب را برون‌بری کنید"

#. translators: 1: New line break, 2: Learn more link.
#: app/modules/import-export/module.php:137
msgid "Design sites faster with a template kit that contains some or all components of a complete site, like templates, content & site settings.%1$sYou can import a kit and apply it to your site, or export the elements from this site to be used anywhere else. %2$s"
msgstr "سایت‌ها را با یک کیت قالب که شامل برخی یا همه اجزای یک سایت کامل است، مانند قالب‌ها، محتوا و تنظیمات سایت، سریع‌تر طراحی کنید. %1$sمی‌توانید یک کیت را وارد کنید و آن را در سایت خود اعمال کنید، یا عناصر را از این سایت صادر کنید. در هر جای دیگری استفاده می شود. %2$s"

#: core/kits/documents/tabs/settings-layout.php:217
msgid "Mobile and Tablet options cannot be deleted."
msgstr "گزینه‌های تلفن همراه و تبلت قابل حذف نیستند."

#: core/utils/import-export/wp-import.php:1143
msgid "The uploaded file could not be moved"
msgstr "پرونده بارگذاری شده قابل انتقال نیست"

#: core/utils/import-export/wp-import.php:1127
msgid "Sorry, this file type is not permitted for security reasons."
msgstr "متأسفیم، این نوع پرونده به دلایل امنیتی مجاز نیست."

#: core/utils/import-export/wp-import.php:1039
msgid "Could not create temporary file."
msgstr "پرونده موقت (temporary file) ایجاد نشد."

#: core/utils/import-export/wp-import.php:253
msgid "The file does not exist, please try again."
msgstr "پرونده وجود ندارد، لطفاً دوباره امتحان کنید."

#: modules/shapes/module.php:48
msgid "Spiral"
msgstr "مارپیچ"

#: modules/shapes/module.php:47
msgid "Oval"
msgstr "بیضی"

#: modules/shapes/module.php:44
msgid "Arc"
msgstr "کمان (Arc)"

#: modules/shapes/widgets/text-path.php:205
msgid "LTR"
msgstr "چپ به راست"

#: modules/shapes/widgets/text-path.php:204
msgid "RTL"
msgstr "راست به چپ"

#: modules/shapes/widgets/text-path.php:199
msgid "Text Direction"
msgstr "جهت متن"

#: app/modules/import-export/module.php:133 core/admin/admin-notices.php:373
#: core/admin/admin-notices.php:429 core/experiments/manager.php:317
#: core/experiments/manager.php:333 core/experiments/manager.php:362
#: core/experiments/manager.php:539 includes/controls/url.php:78
#: includes/elements/section.php:473 includes/settings/settings-page.php:404
#: includes/widgets/common-base.php:1029 includes/widgets/video.php:584
#: modules/ai/feature-intro/product-image-unification-intro.php:40
#: modules/checklist/steps/step-base.php:102
#: modules/editor-app-bar/module.php:50 modules/nested-elements/module.php:21
#: modules/shapes/widgets/text-path.php:150 assets/js/app.js:7604
#: assets/js/editor.js:28094
#: assets/js/kit-library.b0f0ab89c95fe1f6fde3.bundle.js:3497
msgid "Learn more"
msgstr "بیشتر بدانید"

#: modules/shapes/widgets/text-path.php:136
msgid "SVG"
msgstr "SVG"

#: modules/shapes/module.php:43
msgid "Wave"
msgstr "موج"

#: modules/shapes/widgets/text-path.php:485
msgid "Path"
msgstr "مسیر"

#: modules/shapes/widgets/text-path.php:382
msgid "Starting Point"
msgstr "نقطه شروع"

#: includes/controls/groups/typography.php:245
#: modules/shapes/widgets/text-path.php:347
#: assets/js/packages/editor-editing-panel/editor-editing-panel.js:8
#: assets/js/packages/editor-editing-panel/editor-editing-panel.strings.js:36
msgid "Word Spacing"
msgstr "فاصله کلمات"

#: modules/shapes/widgets/text-path.php:217
msgid "Show Path"
msgstr "مسیر نمایش"

#: modules/shapes/widgets/text-path.php:126
msgid "Path Type"
msgstr "نوع مسیر"

#: modules/shapes/widgets/text-path.php:114
msgid "Add Your Curvy Text Here"
msgstr "متن منحنی خود را اینجا اضافه کنید"

#: core/utils/import-export/wp-import.php:995
msgid "Invalid file type"
msgstr "نوع فایل نامعتبر است"

#: core/utils/import-export/wp-import.php:978
msgid "Fetching attachments is not enabled"
msgstr "واکشی پیوست ها فعال نیست"

#: core/utils/import-export/parsers/wxr-parser-simple-xml.php:57
#: core/utils/import-export/parsers/wxr-parser-simple-xml.php:65
msgid "There was an error when reading this WXR file"
msgstr "خطایی در خواندن پروندهٔ WXR رخ داد"

#: core/utils/import-export/parsers/wxr-parser-regex.php:146
#: core/utils/import-export/parsers/wxr-parser-simple-xml.php:70
#: core/utils/import-export/parsers/wxr-parser-simple-xml.php:76
#: core/utils/import-export/parsers/wxr-parser-xml.php:190
msgid "This does not appear to be a WXR file, missing/invalid WXR version number"
msgstr "به نظر نمی رسد این یک پرونده WXR باشد ، شماره نسخه WXR از دست رفته / نامعتبر باشد"

#: modules/shapes/widgets/text-path.php:520
#: modules/shapes/widgets/text-path.php:591
msgid "Stroke"
msgstr "کادر"

#: core/utils/import-export/wp-import.php:1087
msgid "Downloaded file has incorrect size"
msgstr "حجم فایل بارگیری شده اشتباه است"

#: core/utils/import-export/wp-import.php:1081
msgid "Zero size file downloaded"
msgstr "فایل اندازه صفر دانلود شده است"

#: core/utils/import-export/wp-import.php:1073
msgid "Remote server did not respond"
msgstr "سرور از راه دور پاسخ نداد"

#. translators: %s: Menu slug.
#: core/utils/import-export/wp-import.php:891
msgid "Menu item skipped due to invalid menu slug: %s"
msgstr "موردی از گزینگان به سبب نام اینترنتی گزینگان نامعتبر، نادیده گرفته شد: %s"

#: modules/shapes/widgets/text-path.php:51
#: modules/shapes/widgets/text-path.php:103
#: modules/shapes/widgets/text-path.php:243
msgid "Text Path"
msgstr "مسیر متن"

#. translators: 1: WordPress error message, 2: WordPress error code.
#: core/utils/import-export/wp-import.php:1055
msgid "Request failed due to an error: %1$s (%2$s)"
msgstr "درخواست ناموفق بود چراکه خطایی رخ داده است: %1$s (%2$s)"

#. translators: 1: Term taxonomy, 2: Term name.
#. translators: 1: Post type singular label, 2: Post title.
#. translators: 1: Taxonomy name, 2: Term name.
#: core/utils/import-export/wp-import.php:485
#: core/utils/import-export/wp-import.php:676
#: core/utils/import-export/wp-import.php:726
msgid "Failed to import %1$s %2$s"
msgstr "درونریزی %1$s %2$s ناموفق بود"

#: core/kits/documents/tabs/settings-layout.php:215
msgid "Active Breakpoints"
msgstr "نقاط شکست فعال"

#. translators: %s: Max file size.
#: core/utils/import-export/wp-import.php:1095
msgid "Remote file is too large, limit is %s"
msgstr "فایل راه دور بسیار بزرگ است ! محدودیت %s است."

#. translators: 1: Post title, 2: Post type.
#: core/utils/import-export/wp-import.php:584
msgid "Failed to import %1$s: Invalid post type %2$s"
msgstr "درون‌ریزی %2$s پست تایپ ها %1$s با خطا مواجد شد! "

#: core/utils/import-export/wp-import.php:878
msgid "Menu item skipped due to missing menu slug"
msgstr "منو به دلیل عدم وجود اسلاگ نادیده گرفته شد!"

#. translators: 1: HTTP error message, 2: HTTP error code.
#: core/utils/import-export/wp-import.php:1064
msgid "Remote server returned the following unexpected result: %1$s (%2$s)"
msgstr "سرور راه دور نتیجه غیرمنتظره زیر را برگرداند: %1$s (%2$s)"

#. translators: %s: Author display name.
#: core/utils/import-export/wp-import.php:385
msgid "Failed to create new user for %s. Their posts will be attributed to the current user."
msgstr "کاربر جدید برای %s ایجاد نشد. پست های آنها به کاربر فعلی نسبت داده می شود."

#. translators: %s: Post author.
#: core/utils/import-export/wp-import.php:320
msgid "Failed to import author %s. Their posts will be attributed to the current user."
msgstr "وارد کردن نویسنده %s انجام نشد. پست های آنها به کاربر فعلی نسبت داده می شود"

#: core/admin/notices/elementor-dev-notice.php:81
msgid "Install & Activate"
msgstr "نصب و فعالسازی"

#: core/admin/notices/elementor-dev-notice.php:75
msgid "Elementor Developer Edition"
msgstr "نسخه توسعه دهنده المنتور"

#: core/experiments/manager.php:684
msgid "Inactive by default"
msgstr "به صورت پیشفرض غیرفعال است"

#: core/experiments/manager.php:683
msgid "Active by default"
msgstr "به صورت پیشفرض فعال است"

#. translators: %s Release status.
#: core/experiments/manager.php:570
msgid "Status: %s"
msgstr "وضعیت: %s"

#: core/common/modules/finder/categories/settings.php:69
msgid "Experiments"
msgstr "تجربیات"

#: core/experiments/manager.php:412
msgid "Stable"
msgstr "پایدار"

#: core/experiments/manager.php:470
msgid "No available experiments"
msgstr "آزمایش در دسترس نیست"

#: core/experiments/manager.php:410
msgid "Alpha"
msgstr "آلفا"

#: core/experiments/manager.php:409
msgid "Development"
msgstr "توسعه"

#: core/experiments/manager.php:473
msgid "The current version of Elementor doesn't have any experimental features . if you're feeling curious make sure to come back in future versions."
msgstr "نسخه فعلی المنتور هیچ ویژگی آزمایشی ندارد. اگر احساس کنجکاوی می کنید حتما در نسخه های آینده بازگردید."

#: core/admin/notices/elementor-dev-notice.php:76
msgid "Get a sneak peek at our in progress development versions, and help us improve Elementor to perfection. Developer Edition releases contain experimental functionality for testing purposes."
msgstr "به نسخه های در حال پیشرفت ما سری بزنید و به ما کمک کنید تا المنتور را به بهترین شکل توسعه دهیم.  نسخه توسعه دهنده دارای قابلیت های آزمایشی برای اهداف آزمایش هستند."

#: core/experiments/manager.php:411 assets/js/ai-admin.js:657
#: assets/js/ai-admin.js:7776 assets/js/ai-gutenberg.js:2425
#: assets/js/ai-gutenberg.js:9624 assets/js/ai-layout.js:489
#: assets/js/ai-layout.js:3257 assets/js/ai-media-library.js:2286
#: assets/js/ai-media-library.js:9405 assets/js/ai-unify-product-images.js:2286
#: assets/js/ai-unify-product-images.js:9405 assets/js/ai.js:3065
#: assets/js/ai.js:10852
msgid "Beta"
msgstr "بتا"

#: core/experiments/manager.php:538
msgid "To use an experiment or feature on your site, simply click on the dropdown next to it and switch to Active. You can always deactivate them at any time."
msgstr "برای استفاده از یک آزمایش یا ویژگی در سایت خود، کافی است روی منوی کشویی کنار آن کلیک کنید و به فعال بروید. شما همیشه می‌توانید آنها را در هر زمان غیرفعال کنید."

#: modules/compatibility-tag/views/plugin-update-message-compatibility.php:42
#: assets/js/element-manager-admin.js:2298
#: assets/js/element-manager-admin.js:2359
msgid "Plugin"
msgstr "پلاگین"

#: modules/compatibility-tag/views/plugin-update-message-compatibility.php:28
msgid "Compatibility Alert"
msgstr "هشدار سازگاری"

#: modules/landing-pages/module.php:292
msgid "No landing pages found in trash"
msgstr "هیچ صفحه فرود در سطل زباله یافت نشد"

#: modules/landing-pages/module.php:291
msgid "No landing pages found"
msgstr "هیچ صفحه فرودی یافت نشد"

#: modules/landing-pages/module.php:290
msgid "Search Landing Pages"
msgstr "جستجوی صفحات فرود"

#: modules/landing-pages/module.php:289
msgid "View Landing Page"
msgstr "مشاهده صفحه فرود"

#: modules/landing-pages/module.php:288
msgid "All Landing Pages"
msgstr "همه صفحات فرود"

#: modules/landing-pages/module.php:287
msgid "New Landing Page"
msgstr "صفحه فرود جدید"

#: modules/landing-pages/module.php:286
msgid "Edit Landing Page"
msgstr "ویرایش صفحه فرود"

#: modules/landing-pages/module.php:285
msgid "Add New Landing Page"
msgstr "افزودن صفحه فرود جدید"

#: modules/landing-pages/module.php:217
msgid "Build Effective Landing Pages for your business' marketing campaigns."
msgstr "برای فعالیت های بازاریابی کسب و کار خود صفحات فرود موثر ایجاد کنید."

#: modules/landing-pages/admin-menu-items/landing-pages-menu-item.php:22
#: modules/landing-pages/admin-menu-items/landing-pages-menu-item.php:26
#: modules/landing-pages/documents/landing-page.php:54
#: modules/landing-pages/module.php:45 modules/landing-pages/module.php:151
#: modules/landing-pages/module.php:282 modules/landing-pages/module.php:294
#: assets/js/app.js:10352 assets/js/editor.js:52597
msgid "Landing Pages"
msgstr "صفحات فرود"

#: modules/landing-pages/documents/landing-page.php:46
#: modules/landing-pages/module.php:217 modules/landing-pages/module.php:283
msgid "Landing Page"
msgstr "صفحه فرود"

#: modules/compatibility-tag/compatibility-tag-report.php:123
#: modules/compatibility-tag/views/plugin-update-message-compatibility.php:57
#: modules/element-manager/ajax.php:139
msgid "Unknown"
msgstr "ناشناخته"

#. translators: %s: Elementor plugin name.
#: modules/compatibility-tag/views/plugin-update-message-compatibility.php:45
msgid "Tested up to %s version"
msgstr "نسخه حداکثر%s تست شده"

#: modules/landing-pages/module.php:46
msgid "Adds a new Elementor content type that allows creating beautiful landing pages instantly in a streamlined workflow."
msgstr "نوع جدیدی از عناصر النمتور را اضافه می کند که به شما امکان می دهد صفحات فرود زیبا را بلافاصله در یک گردش کار ساده ایجاد کنید."

#. translators: 1: Plugin name, 2: Plugin version.
#: modules/compatibility-tag/views/plugin-update-message-compatibility.php:33
msgid "Some of the plugins you’re using have not been tested with the latest version of %1$s (%2$s). To avoid issues, make sure they are all up to date and compatible before updating %1$s."
msgstr "برخی از افزونه هایی که استفاده می کنید با آخرین نسخه %1$s (%2$s) آزمایش نشده اند. برای جلوگیری از بروز مشکلات ، قبل از بروزرسانی %1$s مطمئن شوید که همه آنها بروز و سازگار هستند."

#: includes/elements/section.php:301
msgid "Custom Columns Gap"
msgstr "شکاف ستون‌های سفارشی"

#: core/kits/views/trash-kit-confirmation.php:33
msgid "Keep my settings"
msgstr "نگه‌داری تنظیمات من"

#: core/kits/views/trash-kit-confirmation.php:21
msgid "By removing this template you will delete your entire Site Settings. If this template is deleted, all associated settings: Global Colors & Fonts, Theme Style, Layout, Background, and Lightbox settings will be removed from your existing site. This action can not be undone."
msgstr "با حذف این الگو، کل تنظیمات سایت خود را حذف خواهید کرد. اگر این الگو حذف شود، همه تنظیمات مربوط به آن: از جمله تنظیمات رنگ‌های سراسری و فونت‌ها، استایل‌های قالب، لایه‌ها، پس‌زمینه و لایت‌باکس از سایت فعلی شما حذف می‌شوند. این عملکرد قابل بازگشت نیست."

#: core/kits/views/trash-kit-confirmation.php:17
msgid "Are you sure you want to delete your Site Settings?"
msgstr "آیا مطمئن هستید که می خواهید تنظیمات سایت خود را حذف کنید؟"

#: core/editor/data/globals/endpoints/base.php:34
msgid "The Global value you are trying to use is not available."
msgstr "مقدار جهانی که می خواهید استفاده کنید در دسترس نیست."

#. Description of the plugin
#: elementor.php
msgid "The Elementor Website Builder has it all: drag and drop page builder, pixel perfect design, mobile responsive editing, and more. Get started now!"
msgstr "سازنده وب سایت المنتور همه این موارد را دارد: صفحه ساز به صورت کشیدن و رها کردن، طراحی عالی پیکسل ، ویرایش واکنشگرا برای موبایل و موارد دیگر در حال حاضر آغاز شده است!"

#: includes/controls/media.php:195
msgid "Choose SVG"
msgstr "انتخاب svg"

#: core/kits/documents/tabs/settings-layout.php:350
#: modules/nested-tabs/widgets/nested-tabs.php:432
msgid "Breakpoint"
msgstr "نقطه توقف"

#: core/kits/documents/tabs/global-colors.php:24
#: core/kits/documents/tabs/global-colors.php:43 assets/js/app.js:10360
#: assets/js/editor.js:47184
#: assets/js/styleguide-app.a6e297c616479b98c03d.bundle.js:173
msgid "Global Colors"
msgstr "رنگ های عمومی"

#: core/kits/manager.php:436 includes/editor-templates/hotkeys.php:115
#: assets/js/app.js:10358 assets/js/app.js:10838 assets/js/editor.js:47133
#: assets/js/editor.js:47137 assets/js/editor.js:47147
#: assets/js/packages/editor-app-bar/editor-app-bar.js:12
#: assets/js/packages/editor-app-bar/editor-app-bar.strings.js:8
msgid "Site Settings"
msgstr "تنظیمات سایت"

#: modules/nested-tabs/widgets/nested-tabs.php:371 assets/js/editor.js:47543
msgid "Additional Settings"
msgstr "تنظیمات اضافی"

#: core/settings/editor-preferences/model.php:195 assets/js/editor.js:47551
msgid "Design System"
msgstr "سیستم طراحی"

#: core/kits/documents/tabs/settings-site-identity.php:82
msgid "Site Description"
msgstr "توضیحات سایت"

#: core/kits/documents/tabs/settings-site-identity.php:71
#: assets/js/onboarding.120a44527e5a7209a8e4.bundle.js:1044
msgid "Site Name"
msgstr "نام سایت"

#: core/kits/documents/tabs/settings-site-identity.php:20
msgid "Site Identity"
msgstr "هویت سایت"

#: core/kits/documents/tabs/settings-layout.php:183
#: modules/page-templates/module.php:159
msgid "Theme"
msgstr "پوسته"

#: core/kits/documents/tabs/settings-layout.php:47 assets/js/app.js:10360
msgid "Layout Settings"
msgstr "تنظیمات طرح"

#: core/admin/admin-notices.php:220 includes/settings/settings-page.php:403
msgid "Become a super contributor by opting in to share non-sensitive plugin data and to receive periodic email updates from us."
msgstr "با تصمیم به اشتراک گذاری داده های غیر حساس افزونه و دریافت ایمیل بروزرسانی های ما، به یک عضو فوق العاده تبدیل شوید."

#: core/admin/admin.php:867
msgid "Heads up, Please backup before upgrade!"
msgstr "حواست باشه، قبل از ارتقا نسخه پشتیبان تهیه کنید!"

#: includes/widgets/icon-list.php:213
msgid "Apply Link On"
msgstr "اعمال لینک روی"

#: core/kits/documents/tabs/settings-background.php:65
msgid "Mobile Browser Background"
msgstr "پس زمینه مرورگر های موبایل"

#: core/kits/documents/tabs/settings-background.php:67
msgid "The `theme-color` meta tag will only be available in supported browsers and devices."
msgstr "متا تگ 'theme-color' فقط در مرورگر های پشتیبانی شده و دستگاه ها در دسترس است."

#: core/kits/documents/tabs/settings-layout.php:180
msgid "Default Page Layout"
msgstr "طرح‌بندی پیش فرض"

#: includes/widgets/common-base.php:1045 includes/widgets/image.php:383
msgid "Fill"
msgstr "پر"

#: includes/widgets/image.php:376
msgid "Object Fit"
msgstr "متناسب با Object"

#: core/kits/documents/tabs/settings-site-identity.php:125
msgid "Suggested favicon dimensions: 512 × 512 pixels."
msgstr "ابعاد پیشنهاد آیکون سایت: 512 × 512 پیکسل."

#: core/kits/documents/tabs/settings-layout.php:194
msgid "Breakpoints"
msgstr "نقطه شکست"

#: core/kits/documents/tabs/settings-site-identity.php:84
msgid "Choose description"
msgstr "یک توضیحات انتخاب کنید"

#: core/kits/documents/tabs/settings-site-identity.php:73
msgid "Choose name"
msgstr "یک نام انتخاب کنید"

#: includes/frontend.php:1411
msgid "Download"
msgstr "دانلود"

#: includes/controls/groups/flex-container.php:24
#: includes/controls/groups/grid-container.php:26
#: includes/widgets/icon-list.php:180
#: modules/nested-accordion/widgets/nested-accordion.php:146
msgid "Items"
msgstr "آیتم ها"

#: core/kits/documents/tabs/settings-site-identity.php:118
msgid "Site Favicon"
msgstr "آیکون سایت"

#: core/kits/documents/tabs/settings-site-identity.php:93
#: assets/js/onboarding.120a44527e5a7209a8e4.bundle.js:1048
msgid "Site Logo"
msgstr "لوگو سایت"

#: core/settings/editor-preferences/model.php:39
#: includes/editor-templates/hotkeys.php:153 assets/js/editor.js:38249
#: assets/js/packages/editor-app-bar/editor-app-bar.js:12
#: assets/js/packages/editor-app-bar/editor-app-bar.strings.js:15
msgid "User Preferences"
msgstr "تنظیمات کاربر"

#: modules/compatibility-tag/compatibility-tag-report.php:173
msgid "Error"
msgstr "خطا"

#. translators: %1$s Link open tag, %2$s: Link close tag.
#: core/admin/admin.php:873
msgid "The latest update includes some substantial changes across different areas of the plugin. We highly recommend you %1$sbackup your site before upgrading%2$s, and make sure you first update in a staging environment"
msgstr "آخرین بروزرسانی شامل برخی تغییرات اساسی در مناطق مختلف افزونه است. اکیداً توصیه می‌کنیم %1$sاز سایت خود قبل از ارتقا نسخه پشتیبان تهیه کنید%2$s و مطمئن شوید که ابتدا در یک محیط استیجینگ بروزرسانی می‌کنید."

#. translators: 1: Width number pixel, 2: Height number pixel.
#: core/kits/documents/tabs/settings-site-identity.php:102
msgid "Suggested image dimensions: %1$s × %2$s pixels."
msgstr "ابعاد پیشنهاد تصویر: %1$s × %2$s پیکسل."

#: modules/page-templates/module.php:365
msgid "Changes will be reflected in the preview only after the page reloads."
msgstr "تغییرات بعد از بارگیری مجدد صفحه، در پیشنمایش در دسترس خواهند بود."

#: core/kits/documents/tabs/global-typography.php:197
msgid "Fallback Font Family"
msgstr "خانواده فونت جایگزین"

#: includes/widgets/social-icons.php:485
msgid "Rows Gap"
msgstr "شکاف ردیف‌ها"

#: core/common/modules/connect/apps/base-app.php:109
msgid "Reset Data"
msgstr "بازگردانی داده ها"

#: includes/controls/media.php:270
msgid "Click the media icon to upload file"
msgstr "برای آپلود فایل، روی آیکون کلیک کنید"

#: includes/settings/settings.php:353 assets/js/admin.js:294
#: assets/js/admin.js:304 assets/js/ai-admin.js:64 assets/js/ai-admin.js:74
#: assets/js/ai-gutenberg.js:1693 assets/js/ai-gutenberg.js:1703
#: assets/js/ai-media-library.js:1693 assets/js/ai-media-library.js:1703
#: assets/js/ai-unify-product-images.js:1693
#: assets/js/ai-unify-product-images.js:1703 assets/js/ai.js:1693
#: assets/js/ai.js:1703 assets/js/common.js:2146 assets/js/common.js:2156
#: assets/js/editor.js:40031 assets/js/editor.js:40041
msgid "Enable Unfiltered File Uploads"
msgstr "فعال سازی آپلود فایل های فیلتر نشده"

#: includes/settings/admin-menu-items/getting-started-menu-item.php:62
msgid "Get introduced to Elementor by watching our \"Getting Started\" video series. It will guide you through the steps needed to create your website. Then click to create your first page."
msgstr "با تماشای مجموعه های ویدیویی \"شروع کار\" با المنتور آشنا شوید. این مرحله شما را از طریق مراحل لازم برای ایجاد وب سایت راهنمایی می کند. سپس برای ایجاد صفحه اول خود کلیک کنید."

#: includes/settings/admin-menu-items/getting-started-menu-item.php:74
msgid "Watch the Full Guide"
msgstr "مشاهده راهنمایی کامل"

#: modules/safe-mode/module.php:367
msgid "If you are experiencing a loading issue, contact your site administrator to troubleshoot the problem using Safe Mode."
msgstr "اگر کندی سرعت را تجربه می کنید، با مدیر سایت برای فعال کردن حالت ایمن در تماس باشید."

#: core/kits/documents/tabs/theme-style-typography.php:48
msgid "Body"
msgstr "بدنه"

#. translators: %s: Widget title.
#: core/editor/promotion.php:54
msgid "%s Widget"
msgstr "%s ویجت"

#: core/kits/documents/tabs/theme-style-form-fields.php:99
msgid "Field"
msgstr "فیلد"

#: includes/managers/controls.php:1204
msgid "Attributes"
msgstr "ویژگی‌ها"

#: includes/frontend.php:1412
msgid "Download image"
msgstr "دانلود تصویر"

#: core/kits/documents/tabs/theme-style-typography.php:75
#: includes/widgets/text-editor.php:306
msgid "Paragraph Spacing"
msgstr "فاصله پاراگراف"

#: includes/controls/url.php:77
msgid "Set custom attributes for the link element. Separate attribute keys from values using the | (pipe) character. Separate key-value pairs with a comma."
msgstr "خصوصیات سفارشی را برای anchor تنظیم کنید. با استفاده از کاراکتر | (pipe) مقدارها را از کلید ها جدا کنید. مقدار-کلید را هم با استفاده از کاما جدا کنید."

#: includes/managers/icons.php:490
msgid "The upgrade process includes a database update"
msgstr "فرایند بروزرسانی شامل بروزرسانی پایگاه داده است"

#: includes/managers/icons.php:491
msgid "We highly recommend backing up your database before performing this upgrade."
msgstr "ما به شدت توصیه می کنیم قبل از انجام این بروزرسانی، از پایگاه داده خود نسخه پشتیبان تهیه کنید."

#: core/base/db-upgrades-manager.php:118
msgid "Click here to run it now"
msgstr "برای اجرا اینجا را کلیک کنید"

#: core/kits/documents/tabs/settings-lightbox.php:102
#: core/kits/documents/tabs/settings-lightbox.php:119
msgid "Alt"
msgstr "Alt"

#: includes/managers/controls.php:1216
msgid "Attributes lets you add custom HTML attributes to any element."
msgstr "خصوصیات به شما امکان اضافه کردن خصوصیات HTML را به هر المنتی می دهد."

#: core/experiments/manager.php:104 includes/editor-templates/global.php:27
#: assets/js/ai-admin.js:9570 assets/js/ai-gutenberg.js:11418
#: assets/js/ai-media-library.js:11199
#: assets/js/ai-unify-product-images.js:11199 assets/js/ai.js:12646
#: assets/js/app.js:7176 assets/js/editor.js:47439
msgid "Back"
msgstr "قبلی"

#: core/kits/documents/tabs/theme-style-buttons.php:23
#: core/kits/documents/tabs/theme-style-buttons.php:63
#: modules/floating-buttons/base/widget-contact-button-base.php:221
msgid "Buttons"
msgstr "دکمه ها"

#: includes/controls/url.php:119
msgid "Custom Attributes"
msgstr "خصوصیات سفارشی"

#: core/isolation/elementor-adapter.php:28 core/kits/manager.php:156
#: core/kits/manager.php:174
msgid "Default Kit"
msgstr "Kit پیش‌ فرض"

#: includes/editor-templates/panel.php:322
msgid "Elementor Dynamic Content"
msgstr "محتوا داینامیک المنتور"

#: core/kits/documents/tabs/theme-style-form-fields.php:128
msgid "Focus"
msgstr "تمرکزی"

#: core/kits/documents/tabs/theme-style-form-fields.php:21
#: core/kits/documents/tabs/theme-style-form-fields.php:60
msgid "Form Fields"
msgstr "فیلد های فرم"

#: core/kits/documents/tabs/settings-lightbox.php:66 includes/frontend.php:1413
msgid "Fullscreen"
msgstr "تمام‌صفحه"

#: includes/editor-templates/panel.php:326
msgid "Get more dynamic capabilities by incorporating dozens of Elementor's native dynamic tags."
msgstr "با ترکیب ده ها برچسب داینامیک، ترکیب های پویا تری را به دست آورید."

#: core/kits/documents/kit.php:43
msgid "Kit"
msgstr "کیت"

#: includes/managers/controls.php:1214
msgid "Meet Our Attributes"
msgstr "مشاهده Attributes های ما"

#: core/kits/documents/tabs/settings-lightbox.php:187
msgid "Navigation Icons Size"
msgstr "سایز آیکون های ناوبری"

#: includes/frontend.php:1410
msgid "Pin it"
msgstr "پین کنید"

#: core/kits/documents/tabs/settings-lightbox.php:86 includes/frontend.php:1415
msgid "Share"
msgstr "اشتراک گذاری"

#: includes/frontend.php:1408
msgid "Share on Facebook"
msgstr "اشتراک گذاری در Facebook"

#: includes/frontend.php:1409
msgid "Share on Twitter"
msgstr "اشتراک گذاری در Twitter"

#. translators: %s: Widget title.
#: core/editor/promotion.php:56
msgid "Use %s widget and dozens more pro features to extend your toolbox and build sites faster and better."
msgstr "با استفاده از ویجت %s و ده ها قابلیت پرو، سایت های سریعتر و بهتری طراحی کنید."

#: includes/editor-templates/panel.php:325
msgid "You’re missing out!"
msgstr "دلتنگ شدی!"

#: core/kits/documents/tabs/theme-style-form-fields.php:71
msgid "Label"
msgstr "لیبل"

#: core/kits/documents/kit.php:154
msgid "Draft"
msgstr "پیشنویس"

#: core/kits/documents/tabs/settings-lightbox.php:174
msgid "Toolbar Icons Size"
msgstr "اندازه آیکون‌های نوار ابزار"

#: includes/editor-templates/panel.php:296
#: assets/js/packages/editor-editing-panel/editor-editing-panel.js:8
#: assets/js/packages/editor-editing-panel/editor-editing-panel.strings.js:28
#: assets/js/packages/editor-editing-panel/editor-editing-panel.strings.js:34
msgid "Dynamic Tags"
msgstr "برچسب‌های پویا"

#: core/common/modules/connect/apps/base-app.php:161
msgid "Already connected."
msgstr "متصل شده است."

#: core/settings/editor-preferences/model.php:173 assets/js/ai-admin.js:15860
#: assets/js/ai-gutenberg.js:17708 assets/js/ai-layout.js:5210
#: assets/js/ai-media-library.js:17489
#: assets/js/ai-unify-product-images.js:17489 assets/js/ai.js:9372
#: assets/js/ai.js:18936 assets/js/editor.js:10033
#: assets/js/kit-library.b0f0ab89c95fe1f6fde3.bundle.js:658
msgid "Get Started"
msgstr "شروع کنید"

#. translators: %s: Video provider
#: includes/embed.php:185
msgid "%s Video Player"
msgstr "%s پخش کننده ویدئو"

#: includes/controls/groups/background.php:485
#: includes/controls/groups/background.php:690 includes/widgets/image.php:384
#: modules/link-in-bio/base/widget-link-in-bio-base.php:920
#: modules/link-in-bio/base/widget-link-in-bio-base.php:975
#: assets/js/packages/editor-controls/editor-controls.js:10
msgid "Cover"
msgstr "پوشش"

#: includes/settings/settings-page.php:396
msgid "Usage Data Sharing"
msgstr "اشتراک داده های استفاده"

#: core/common/modules/connect/apps/library.php:29
#: core/common/modules/connect/apps/library.php:56
msgid "Connecting to the Library failed. Please try reloading the page and try again"
msgstr "اتصال به کتابخانه انجام نشد. لطفا صفحه را مجدد بارگذاری کنید و دوباره امتحان کنید"

#: core/settings/editor-preferences/model.php:79
msgid "Auto detect"
msgstr "شناسایی خودکار"

#: includes/controls/groups/background.php:702
msgid "Background Position"
msgstr "موقعیت پس زمینه"

#: includes/controls/groups/background.php:683
msgid "Background Size"
msgstr "سایز پس‌زمینه"

#. translators: %s: Remote user.
#: core/common/modules/connect/apps/base-app.php:84
msgid "Connected as %s"
msgstr "متصل شده به عنوان %s"

#: core/kits/documents/tabs/settings-background.php:81
#: includes/controls/groups/background.php:486
#: includes/controls/groups/background.php:691 includes/widgets/image.php:385
#: assets/js/packages/editor-controls/editor-controls.js:10
msgid "Contain"
msgstr "دربرگیرنده"

#: includes/widgets/image-carousel.php:462
msgid "Pause on Interaction"
msgstr "مکث در واکنش"

#: core/settings/editor-preferences/model.php:52
msgid "Preferences"
msgstr "مزیت ها"

#: core/kits/documents/tabs/settings-background.php:80
#: includes/controls/groups/background.php:484
#: includes/controls/groups/background.php:689
#: includes/elements/container.php:547 includes/widgets/social-icons.php:299
#: includes/widgets/video.php:578
#: assets/js/packages/editor-controls/editor-controls.js:10
#: assets/js/packages/editor-editing-panel/editor-editing-panel.js:8
#: assets/js/packages/editor-editing-panel/editor-editing-panel.strings.js:72
msgid "Auto"
msgstr "خودکار"

#: modules/promotions/admin-menu-items/custom-icons-promotion-item.php:15
#: modules/promotions/admin-menu-items/custom-icons-promotion-item.php:19
msgid "Custom Icons"
msgstr "آیکون‌های سفارشی"

#: includes/controls/groups/background.php:645
msgid "Duration"
msgstr "مدت زمان"

#: core/logger/log-reporter.php:44
msgid "Clear Log"
msgstr "پاک کردن لاگ"

#: includes/controls/groups/background.php:655
msgid "Transition"
msgstr "انتقال"

#: includes/frontend.php:1418 assets/js/app.js:7115 assets/js/app.js:8795
#: assets/js/app.js:9738
#: assets/js/onboarding.120a44527e5a7209a8e4.bundle.js:1676
#: assets/js/onboarding.120a44527e5a7209a8e4.bundle.js:1727
#: assets/js/onboarding.120a44527e5a7209a8e4.bundle.js:2006
#: assets/js/onboarding.120a44527e5a7209a8e4.bundle.js:2283
msgid "Next"
msgstr "بعدی"

#: includes/frontend.php:1417 assets/js/app.js:7988 assets/js/app.js:8782
#: assets/js/app.js:9731
msgid "Previous"
msgstr "قبلی"

#: includes/widgets/divider.php:310
msgctxt "Shapes"
msgid "X"
msgstr "X"

#: includes/widgets/divider.php:173
msgctxt "Shapes"
msgid "Squared"
msgstr "مربعی"

#: includes/controls/groups/background.php:754
msgid "Out"
msgstr "بیرون"

#: includes/controls/groups/background.php:753
msgid "In"
msgstr "در"

#: includes/settings/tools.php:386
msgid "Reinstall"
msgstr "نصب مجدد"

#: core/document-types/post.php:51
msgid "Post"
msgstr "نوشته"

#: includes/widgets/divider.php:292
msgctxt "Shapes"
msgid "Trees"
msgstr "درختان"

#: includes/widgets/divider.php:221
msgctxt "Shapes"
msgid "Parallelogram"
msgstr "متوازی الاضلاع"

#: includes/widgets/divider.php:156
msgctxt "Shapes"
msgid "Multiple"
msgstr "چندگانه"

#: includes/widgets/divider.php:140
msgctxt "Shapes"
msgid "Curly"
msgstr "فرفری"

#: includes/controls/groups/background.php:738
msgid "Ken Burns Effect"
msgstr "افکت Ken Burns"

#: includes/widgets/divider.php:247
msgctxt "Shapes"
msgid "Fir Tree"
msgstr "درخت صنوبر"

#: includes/widgets/image-gallery.php:44 includes/widgets/image-gallery.php:130
msgid "Basic Gallery"
msgstr "گالری پایه"

#: includes/widgets/divider.php:340 modules/shapes/module.php:46
msgid "Line"
msgstr "خطی"

#: includes/widgets/divider.php:197
msgctxt "Shapes"
msgid "Arrows"
msgstr "پیکان‌ها"

#: includes/widgets/divider.php:256
msgctxt "Shapes"
msgid "Half Rounds"
msgstr "نیم دور"

#: includes/widgets/divider.php:213
msgctxt "Shapes"
msgid "Rhombus"
msgstr "لوزی"

#: includes/widgets/divider.php:164
msgctxt "Shapes"
msgid "Slashes"
msgstr "خط فاصله دار"

#: includes/widgets/divider.php:496
#: assets/js/packages/editor-app-bar/editor-app-bar.js:12
#: assets/js/packages/editor-app-bar/editor-app-bar.strings.js:21
msgid "Add Element"
msgstr "افزودن المنت"

#: includes/controls/groups/background.php:583 includes/widgets/video.php:354
msgid "Play On Mobile"
msgstr "نمایش در موبایل"

#: includes/controls/groups/background.php:608
msgid "This cover image will replace the background video in case that the video could not be loaded."
msgstr "در صورت عدم بارگذاری ویدیو، این تصویر جلد جایگزین فیلم پس زمینه خواهد شد."

#: includes/controls/groups/background.php:528
msgid "YouTube/Vimeo link, or link to video file (mp4 is recommended)."
msgstr "لینک یوتیوب یا فایل ویدیویی (فرمت mp4 توصیه می شود)."

#: includes/widgets/divider.php:667
msgid "Amount"
msgstr "میزان"

#: includes/widgets/divider.php:265
msgctxt "Shapes"
msgid "Leaves"
msgstr "برگ‌ها"

#: includes/widgets/divider.php:238
msgctxt "Shapes"
msgid "Dots"
msgstr "نقطه‌چین"

#: includes/widgets/divider.php:148
msgctxt "Shapes"
msgid "Curved"
msgstr "منحنی‌وار"

#: includes/widgets/divider.php:229
msgctxt "Shapes"
msgid "Rectangles"
msgstr "مستطیلی"

#: includes/widgets/divider.php:181
msgctxt "Shapes"
msgid "Wavy"
msgstr "موج‌دار"

#: includes/widgets/divider.php:301
msgctxt "Shapes"
msgid "Tribal"
msgstr "قبیله‌ای"

#: includes/widgets/divider.php:274
msgctxt "Shapes"
msgid "Stripes"
msgstr "راه‌راه"

#: includes/widgets/divider.php:205
msgctxt "Shapes"
msgid "Pluses"
msgstr "بعلاوه"

#: includes/widgets/divider.php:283
msgctxt "Shapes"
msgid "Squares"
msgstr "چهارخانه"

#. translators: %s: Path to .htaccess file.
#: core/debug/classes/htaccess.php:31
msgid "File Path: %s"
msgstr "مسیر فایل: %s"

#: includes/controls/media.php:192
msgid "Choose Video"
msgstr "انتخاب ویدیو"

#: includes/controls/media.php:283 includes/controls/media.php:285
#: assets/js/editor.js:8093
msgid "Upload"
msgstr "بارگذاری"

#: includes/controls/icons.php:90 includes/controls/icons.php:116
#: includes/controls/icons.php:202 assets/js/editor.js:8677
msgid "Icon Library"
msgstr "کتابخانه آیکون"

#: includes/managers/icons.php:505
msgid "Upgrade To Font Awesome 5"
msgstr "ارتقا به Font Awesome 5"

#: includes/managers/icons.php:156
msgid "Font Awesome - Brands"
msgstr "Font Awesome - برندها"

#: includes/managers/icons.php:485
msgid "Access 1,500+ amazing Font Awesome 5 icons and enjoy faster performance and design flexibility."
msgstr "دسترسی به 1500+ آیکون های شگفت انگیز Font Awesome 5 و لذت بردن از عملکرد سریع و انعطاف پذیری طراحی."

#: includes/admin-templates/beta-tester.php:30
msgid "As a beta tester, you’ll receive an update that includes a testing version of Elementor and its content directly to your Email"
msgstr "به عنوان یک آزمایشگر بتا، شما یک بروزرسانی در ایمیلتان دریافت خواهید کرد که شامل نسخه آزمایشی المنتور و محتوای آن می باشد."

#: includes/managers/icons.php:488
msgid "Please note that the upgrade process may cause some of the previously used Font Awesome 4 icons to look a bit different due to minor design changes made by Font Awesome."
msgstr "لطفا توجه داشته باشید که روند ارتقاء ممکن است بعضی از آیکون های Font Awesome 4 قبلی که قبلا مورد استفاده قرار گرفته اند، به دلیل تغییرات جزئی طراحی شده توسط Font Awesome کمی متفاوت باشند."

#: includes/managers/icons.php:248
msgid "All Icons"
msgstr "همه آیکون ها"

#: includes/managers/icons.php:486
msgid "By upgrading, whenever you edit a page containing a Font Awesome 4 icon, Elementor will convert it to the new Font Awesome 5 icon."
msgstr "با ارتقاء، هر زمان که یک صفحه را حاوی آیکون Font Awesome 4 ویرایش کنید، Elementor آن را به آیکون جدید Font Awesome 5 تبدیل خواهد کرد."

#: core/experiments/manager.php:664 includes/base/widget-base.php:1019
msgid "Deprecated"
msgstr "منسوخ"

#: core/editor/editor.php:210
msgid "Document not found."
msgstr "مستندی یافت نشد."

#: includes/settings/settings.php:361
msgid "Elementor will try to sanitize the unfiltered files, removing potential malicious code and scripts."
msgstr "المنتور سعی میکند فایل ها را پاکسازی کند، کد های مخرب و اسکریپت های احتمالی را حذف کند."

#: includes/managers/icons.php:132
msgid "Font Awesome - Regular"
msgstr "Font Awesome - عادی"

#: includes/managers/icons.php:144
msgid "Font Awesome - Solid"
msgstr "Font Awesome - یکپارچه"

#: includes/managers/icons.php:479 includes/managers/icons.php:483
#: includes/managers/icons.php:498
msgid "Font Awesome Upgrade"
msgstr "ارتقا Font Awesome"

#: includes/admin-templates/beta-tester.php:29
msgid "Get Beta Updates"
msgstr "دریافت بروزرسانی بتا"

#: includes/managers/icons.php:551
msgid "Hurray! The upgrade process to Font Awesome 5 was completed successfully."
msgstr "هورا! ارتقا به Font Awesome 5 با موفقیت به پایان رسید."

#: includes/managers/icons.php:464
msgid "Load Font Awesome 4 Support"
msgstr "پشتیبانی از بارگذاری Font Awesome 4"

#: includes/controls/groups/background.php:573
msgid "Play Once"
msgstr "نمایش یک بار"

#: includes/settings/settings.php:361
msgid "Please note! Allowing uploads of any files (SVG & JSON included) is a potential security risk."
msgstr "لطفا توجه کنید! آپلود کردن هر فایلی (شامل SVG و JSON) دارای ریسک امنیتی است."

#: includes/admin-templates/beta-tester.php:37 assets/js/beta-tester.js:64
msgid "Sign Up"
msgstr "ثبت نام"

#: core/debug/classes/theme-missing.php:22
msgid "Some of your theme files are missing."
msgstr "برخی از فایل های قالب شما یافت نشد است."

#: includes/template-library/sources/local.php:618
msgid "Template not exist."
msgstr "قالب وجود ندارد."

#: includes/managers/icons.php:493
msgid "This action is not reversible and cannot be undone by rolling back to previous versions."
msgstr "این عمل غیرقابل برگشت است و با تنزیل دادن به نسخه های قبلی نمی توان آن را لغو کرد."

#: includes/controls/icons.php:91 includes/controls/icons.php:112
#: includes/controls/icons.php:198
msgid "Upload SVG"
msgstr "آپلود فایل SVG"

#: includes/settings/settings.php:361
msgid "We recommend you only enable this feature if you understand the security risks involved."
msgstr "توصیه میکنیم اگر این ریسک امنیتی این مورد را درک می کنید، تنها این قابلیت را فعال کنید."

#: includes/admin-templates/beta-tester.php:32
msgid "Your Email"
msgstr "ایمیل"

#: core/debug/classes/htaccess.php:12
msgid "Your site's .htaccess file appears to be missing."
msgstr "ظاهرا فایل htaccess سایت شما از دست رفته است."

#: core/kits/views/panel.php:12 includes/editor-templates/panel.php:30
#: includes/editor-templates/panel.php:183
msgid "Need Help"
msgstr "درخواست کمک"

#: core/files/file-types/svg.php:73 core/files/uploads-manager.php:590
msgid "This file is not allowed for security reasons."
msgstr "این فایل به دلایل امنیتی مجاز نیست."

#: includes/managers/icons.php:472
msgid "Font Awesome 4 support script (shim.js) is a script that makes sure all previously selected Font Awesome 4 icons are displayed correctly while using Font Awesome 5 library."
msgstr "اسکریپت پشتیبانی (shim.js) Font Awesome 4 اسکریپتی است که اطمینان می‌دهد که تمام آیکون‌های Font Awesome 4 که قبلا انتخاب شده‌اند، هنگام استفاده از کتابخانه Font Awesome 5 به درستی نمایش داده می‌شوند."

#. translators: 1: Link open tag, 2: Link close tag.
#: includes/elements/column.php:928 includes/elements/container.php:1885
#: includes/elements/section.php:1407 includes/widgets/common-base.php:1230
msgid "Responsive visibility will take effect only on %1$s preview mode %2$s or live page, and not while editing in Elementor."
msgstr "قابلیت مشاهده واکنش‌گرا تنها در %1$s حالت پیش‌نمایش %2$s یا صفحه زنده تاثیر دارد و در هنگام ویرایش در المنتور موثر نخواهد بود."

#: includes/controls/groups/flex-item.php:30
#: includes/widgets/common-base.php:249
msgid "Custom Width"
msgstr "عرض سفارشی"

#: includes/elements/container.php:546 includes/elements/section.php:453
#: assets/js/packages/editor-editing-panel/editor-editing-panel.js:8
#: assets/js/packages/editor-editing-panel/editor-editing-panel.strings.js:71
msgid "Hidden"
msgstr "مخفی"

#: includes/elements/container.php:1648 includes/widgets/common-base.php:565
msgid "Vertical Orientation"
msgstr "جهت عمودی"

#: includes/elements/container.php:1547 includes/widgets/common-base.php:464
msgid "Horizontal Orientation"
msgstr "جهت افقی"

#: includes/controls/groups/flex-container.php:113
#: includes/controls/groups/flex-container.php:226
#: includes/controls/groups/grid-container.php:208
#: includes/controls/groups/grid-container.php:248
#: includes/elements/column.php:191 includes/elements/column.php:219
#: includes/elements/section.php:429
msgid "Space Around"
msgstr "فضای اطراف"

#: includes/elements/container.php:1528 includes/widgets/common-base.php:450
#: assets/js/packages/editor-controls/editor-controls.js:10
#: assets/js/packages/editor-editing-panel/editor-editing-panel.js:8
#: assets/js/packages/editor-editing-panel/editor-editing-panel.strings.js:78
msgid "Fixed"
msgstr "ثابت"

#: includes/elements/container.php:1510 includes/widgets/common-base.php:433
msgid "Custom positioning is not considered best practice for responsive web design and should not be used too frequently."
msgstr "موقعیت سفارشی بهترین روش برای طراحی سایت واکنشگرا نیست و نباید بیش از حد از آن استفاده کرد."

#: includes/settings/admin-menu-items/get-help-menu-item.php:23
msgid "Get Help"
msgstr "دریافت راهنمایی"

#: includes/elements/container.php:1509 includes/widgets/common-base.php:432
msgid "Please note!"
msgstr "لطفا توجه داشته باشید!"

#: includes/elements/container.php:1527 includes/widgets/common-base.php:449
#: assets/js/packages/editor-editing-panel/editor-editing-panel.js:8
#: assets/js/packages/editor-editing-panel/editor-editing-panel.strings.js:77
msgid "Absolute"
msgstr "مطلق"

#: includes/elements/column.php:863 includes/elements/container.php:1805
#: includes/elements/section.php:1316 includes/widgets/common-base.php:720
msgid "Motion Effects"
msgstr "موشن افکت"

#: includes/controls/groups/flex-container.php:117
#: includes/controls/groups/flex-container.php:230
#: includes/controls/groups/grid-container.php:212
#: includes/controls/groups/grid-container.php:252
#: includes/elements/column.php:192 includes/elements/column.php:220
#: includes/elements/section.php:430
msgid "Space Evenly"
msgstr "فضا یکنواخت"

#: includes/settings/controls.php:236
msgid "Super Admin"
msgstr "مدیرکل"

#: includes/elements/container.php:541 includes/elements/section.php:448
#: assets/js/packages/editor-editing-panel/editor-editing-panel.js:8
#: assets/js/packages/editor-editing-panel/editor-editing-panel.strings.js:73
msgid "Overflow"
msgstr "Overflow"

#: includes/base/element-base.php:1000 includes/elements/container.php:1572
#: includes/elements/container.php:1610 includes/elements/container.php:1672
#: includes/elements/container.php:1709 includes/widgets/common-base.php:489
#: includes/widgets/common-base.php:527 includes/widgets/common-base.php:589
#: includes/widgets/common-base.php:626
#: modules/floating-buttons/base/widget-contact-button-base.php:2959
#: modules/floating-buttons/base/widget-contact-button-base.php:3013
msgid "Offset"
msgstr "انحراف"

#: includes/elements/section.php:420 includes/widgets/common-base.php:404
#: includes/widgets/image-carousel.php:732
msgid "Vertical Align"
msgstr "تراز عمودی"

#: modules/safe-mode/module.php:358
msgid "Having problems loading Elementor? Please enable Safe Mode to troubleshoot."
msgstr "با بارگذاری المنتور مشکل دارید؟ لطفا حالت امن را برای عیب یابی فعال کنید."

#. translators: %s: Accepted chars.
#: includes/widgets/menu-anchor.php:136
msgid "Note: The ID link ONLY accepts these chars: %s"
msgstr "توجه: لینک شناسه فقط این کاراکتراها را قبول میکند: %s"

#: modules/safe-mode/module.php:256
msgid "The issue was probably caused by one of your plugins or theme."
msgstr "شاید این مشکل توسط یکی از افزونه ها یا قالب اتفاق افتاده است."

#: includes/controls/media.php:198
#: modules/link-in-bio/base/widget-link-in-bio-base.php:387
msgid "Choose File"
msgstr "انتخاب فایل"

#: includes/widgets/video.php:223
msgid "External URL"
msgstr "لینک خارجی"

#: includes/widgets/image-gallery.php:263
msgid "Order By"
msgstr "ترتیب بر اساس"

#: includes/widgets/read-more.php:124
msgid "Read More Text"
msgstr "متن بیشتر بخوانید"

#: includes/widgets/read-more.php:95
msgid "Continue reading"
msgstr "ادامه مطلب"

#: includes/widgets/read-more.php:40 includes/widgets/read-more.php:91
msgid "Read More"
msgstr "بیشتر بخوانید"

#: includes/widgets/google-maps.php:154
#: modules/floating-buttons/base/widget-contact-button-base.php:396
#: modules/floating-buttons/base/widget-contact-button-base.php:956
#: modules/link-in-bio/base/widget-link-in-bio-base.php:500
#: modules/link-in-bio/base/widget-link-in-bio-base.php:750
msgid "Location"
msgstr "مکان"

#. translators: %s: Current post name.
#: includes/frontend.php:1553
msgid "Continue reading %s"
msgstr "ادامه مطلب %s"

#: includes/frontend.php:1546
msgid "(more&hellip;)"
msgstr "(بیشتر&hellip;)"

#: includes/template-library/sources/local.php:1441
msgctxt "Template Library"
msgid "Filter by category"
msgstr "فیلتر بر اساس دسته بندی"

#: includes/template-library/sources/local.php:323
msgctxt "Template Library"
msgid "All Categories"
msgstr "همه دسته بندی ها"

#: includes/template-library/sources/local.php:321
msgctxt "Template Library"
msgid "Categories"
msgstr "دسته بندی ها"

#: modules/promotions/admin-menu-items/popups-promotion-item.php:18
msgid "Get Popup Builder"
msgstr "دریافت پاپ آپ ساز"

#: includes/template-library/sources/local.php:1737
#: modules/promotions/admin-menu-items/popups-promotion-item.php:41
#: modules/promotions/admin-menu-items/popups-promotion-item.php:45
#: assets/js/app.js:10343
#: assets/js/kit-library.b0f0ab89c95fe1f6fde3.bundle.js:2219
msgid "Popups"
msgstr "پاپ آپ"

#: modules/safe-mode/module.php:352 modules/safe-mode/module.php:364
msgid "Can't Edit?"
msgstr "نمی توانید ویرایش کنید؟"

#: modules/safe-mode/module.php:246 modules/safe-mode/module.php:476
msgid "Disable Safe Mode"
msgstr "غیر فعال کردن حالت امن"

#: modules/safe-mode/module.php:102
msgid "Cannot enable Safe Mode"
msgstr "نمی توانم حالت امن را فعال کنم"

#: core/upgrade/manager.php:51
msgid "Elementor Data Updater"
msgstr "بروزرسان داده المنتور"

#. translators: %d: Interval in minutes.
#: core/base/background-process/wp-background-process.php:439
#: core/base/background-task.php:316
msgid "Every %d minutes"
msgstr "هر %d دقیقه"

#: modules/safe-mode/module.php:51
msgid "Safe Mode allows you to troubleshoot issues by only loading the editor, without loading the theme or any other plugin."
msgstr "حالت امن به شما اجازه عیب یابی مشکل فقط با بارگذاری ویرایشگر ، بدون بارگذاری قالب یا هر افزونه ‌ای را میدهد را میدهد."

#: modules/safe-mode/module.php:253
msgid "Editor successfully loaded?"
msgstr "ویرایشگر با موفقیت بارگذاری شد؟"

#: modules/safe-mode/module.php:42
msgid "Safe Mode"
msgstr "حالت ایمن"

#: modules/safe-mode/module.php:269
msgid "Still experiencing issues?"
msgstr "هنوز با مشکل کار می کنید؟"

#: includes/template-library/sources/local.php:322
msgctxt "Template Library"
msgid "Category"
msgstr "دسته بندی"

#: modules/safe-mode/module.php:354
msgid "Enable Safe Mode"
msgstr "فعال‌سازی حالت ایمن"

#: modules/safe-mode/module.php:244
msgid "Safe Mode ON"
msgstr "حالت امن روشن"

#: core/base/db-upgrades-manager.php:130
msgid "The database update process is now complete. Thank you for updating to the latest version!"
msgstr "فرآیند بروزرسانی پایگاه داده در حال حاضر کامل است. با تشکر از شما برای بروزرسانی به آخرین نسخه!"

#: core/base/db-upgrades-manager.php:93
msgid "Your site database needs to be updated to the latest version."
msgstr "پایگاه داده سایت شما باید به آخرین نسخه بروز شود."

#: modules/promotions/admin-menu-items/popups-promotion-item.php:19
msgid "The Popup Builder lets you take advantage of all the amazing features in Elementor, so you can build beautiful & highly converting popups. Get Elementor Pro and start designing your popups today."
msgstr "پاپ آپ ساز به شما امکان می‌دهد از تمام ویژگی‌های شگفت‌انگیز المنتور بهره ببرید، بنابراین می‌توانید پنجره‌های پاپ‌آپ زیبا و بسیار تبدیل‌کننده بسازید. المنتور پرو را دریافت کنید و همین امروز شروع به طراحی پنجره های بازشو کنید."

#: app/admin-menu-items/theme-builder-menu-item.php:22
#: app/modules/site-editor/module.php:31
#: core/common/modules/finder/categories/general.php:72
#: includes/template-library/sources/local.php:1736
#: assets/js/app-packages.js:5890 assets/js/editor.js:47158
#: assets/js/packages/editor-app-bar/editor-app-bar.js:12
#: assets/js/packages/editor-app-bar/editor-app-bar.strings.js:6
msgid "Theme Builder"
msgstr "پوسته‌ساز"

#: core/admin/menu/main.php:34 core/admin/menu/main.php:35
#: includes/template-library/sources/local.php:239
msgctxt "Template Library"
msgid "Templates"
msgstr "قالب‌ها"

#. translators: %s: The `the_content` function.
#: includes/widgets/read-more.php:115
msgid "Note: This widget only affects themes that use `%s` in archive pages."
msgstr "توجه: این ابزارک فقط روی پوسته‌هایی تأثیر می‌گذارد که از «%s» در برگه‌های بایگانی استفاده می‌کنند."

#: modules/library/documents/not-supported.php:56
msgid "Not Supported"
msgstr "پشتیبانی نمیشود"

#: core/common/modules/finder/categories/site.php:82
msgid "Users"
msgstr "کاربران"

#: core/common/modules/finder/categories/site.php:76 assets/js/app.js:10846
msgid "Plugins"
msgstr "افزونه‌ها"

#: includes/widgets/video.php:495
msgid "Any Video"
msgstr "هر ویدئویی"

#: includes/widgets/video.php:494
msgid "Current Video Channel"
msgstr "کانال ویدئو فعلی"

#: includes/widgets/rating.php:22 includes/widgets/rating.php:140
#: includes/widgets/rating.php:164 includes/widgets/star-rating.php:153
msgid "Rating"
msgstr "امتیاز دهی"

#: includes/widgets/image.php:163 includes/widgets/image.php:175
msgid "Custom Caption"
msgstr "کپشن دلخواه"

#: includes/widgets/image-gallery.php:175 includes/widgets/image.php:162
msgid "Attachment Caption"
msgstr "پیوست کپشن"

#: includes/editor-templates/hotkeys.php:107
msgid "Show / Hide Panel"
msgstr "نمایش / مخفی کردن پنل"

#: includes/editor-templates/hotkeys.php:167
msgid "Go To"
msgstr "برو به"

#: includes/editor-templates/hotkeys.php:99 assets/js/admin-top-bar.js:189
#: assets/js/common.js:4633 assets/js/editor.js:38261
#: assets/js/packages/editor-app-bar/editor-app-bar.js:12
#: assets/js/packages/editor-app-bar/editor-app-bar.strings.js:18
msgid "Finder"
msgstr "جستجوگر"

#: core/common/modules/finder/categories/site.php:58
msgid "Menus"
msgstr "فهرست‌ها"

#: core/common/modules/finder/categories/site.php:52
msgid "Dashboard"
msgstr "پیشخوان"

#: core/common/modules/finder/categories/site.php:46
#: assets/js/packages/editor-site-navigation/editor-site-navigation.js:2
#: assets/js/packages/editor-site-navigation/editor-site-navigation.strings.js:26
msgid "Homepage"
msgstr "صفحه اصلی"

#: core/common/modules/finder/categories/create.php:27
#: assets/js/editor.js:10522 assets/js/editor.js:45756
msgid "Create"
msgstr "ایجاد"

#: core/common/modules/connect/apps/connect.php:11
#: core/common/modules/connect/connect-menu-item.php:24
#: core/common/modules/connect/connect-menu-item.php:28
#: modules/cloud-library/module.php:74 modules/cloud-library/module.php:76
#: assets/js/ai-admin.js:1072 assets/js/ai-admin.js:6399
#: assets/js/ai-gutenberg.js:2840 assets/js/ai-gutenberg.js:8247
#: assets/js/ai-layout.js:770 assets/js/ai-layout.js:2492
#: assets/js/ai-media-library.js:2701 assets/js/ai-media-library.js:8028
#: assets/js/ai-unify-product-images.js:2701
#: assets/js/ai-unify-product-images.js:8028 assets/js/ai.js:3480
#: assets/js/ai.js:9383 assets/js/ai.js:9475
msgid "Connect"
msgstr "اتصال"

#: core/base/document.php:1986
msgid "Future"
msgstr "آینده"

#: core/common/modules/finder/template.php:13
msgid "Type to find anything in Elementor"
msgstr "برای پیدا کردن هر چیزی در المنتور تایپ کنید"

#: core/common/modules/connect/apps/base-app.php:220 assets/js/editor.js:11070
#: assets/js/editor.js:11127
msgid "Connected successfully."
msgstr "با موفقیت متصل شد."

#: core/common/modules/connect/apps/base-app.php:87
msgid "Disconnect"
msgstr "تخفیف"

#: includes/widgets/rating.php:147 includes/widgets/star-rating.php:140
msgid "Rating Scale"
msgstr "مقیاس امتیاز دهی"

#: core/common/modules/finder/categories/site.php:70
msgid "Customizer"
msgstr "سفارشی‌ساز"

#: includes/widgets/star-rating.php:319
msgid "Stars"
msgstr "ستاره‌ها"

#: includes/widgets/star-rating.php:45 includes/widgets/star-rating.php:122
msgid "Star Rating"
msgstr "ستاره امتیاز دهی"

#: core/common/modules/connect/apps/base-app.php:232
msgid "Disconnected successfully."
msgstr "اتصال با موفقیت قطع شد."

#: includes/editor-templates/hotkeys.php:208
msgid "Quit"
msgstr "ساکت"

#: includes/editor-templates/hotkeys.php:32 assets/js/ai-admin.js:12544
#: assets/js/ai-admin.js:13633 assets/js/ai-gutenberg.js:14392
#: assets/js/ai-gutenberg.js:15481 assets/js/ai-media-library.js:14173
#: assets/js/ai-media-library.js:15262
#: assets/js/ai-unify-product-images.js:14173
#: assets/js/ai-unify-product-images.js:15262 assets/js/ai.js:15620
#: assets/js/ai.js:16709
msgid "Redo"
msgstr "مجدداً"

#: includes/editor-templates/hotkeys.php:24 assets/js/ai-admin.js:12533
#: assets/js/ai-admin.js:13622 assets/js/ai-gutenberg.js:14381
#: assets/js/ai-gutenberg.js:15470 assets/js/ai-media-library.js:14162
#: assets/js/ai-media-library.js:15251
#: assets/js/ai-unify-product-images.js:14162
#: assets/js/ai-unify-product-images.js:15251 assets/js/ai.js:15609
#: assets/js/ai.js:16698 assets/js/kit-elements-defaults-editor.js:232
msgid "Undo"
msgstr "واگرد"

#: includes/widgets/rating.php:122 includes/widgets/star-rating.php:390
msgid "Unmarked Color"
msgstr "رنگ بدون مارک"

#: includes/widgets/star-rating.php:184
msgid "Unmarked Style"
msgstr "استای بدون مارک"

#: includes/widgets/star-rating.php:192
msgid "Outline"
msgstr "خارج خط"

#: includes/editor-templates/hotkeys.php:200 assets/js/editor.js:7402
#: assets/js/packages/editor-app-bar/editor-app-bar.js:12
#: assets/js/packages/editor-app-bar/editor-app-bar.strings.js:16
msgid "Keyboard Shortcuts"
msgstr "میان‌برهای صفحه کلید"

#: includes/widgets/video.php:597
msgid "Poster"
msgstr "پوستر"

#: includes/settings/admin-menu-items/getting-started-menu-item.php:61
msgid "Welcome to Elementor"
msgstr "به المنتور خوش آمدید"

#: core/admin/admin-notices.php:289
msgid "Hide Notification"
msgstr "مخفی کردن اعلان"

#: core/admin/admin-notices.php:283
msgid "Happy To Help"
msgstr "خوشحال میشم بتونم کمک کنم"

#: core/admin/admin-notices.php:279
msgid "Congrats!"
msgstr "تبریک میگم!"

#: includes/widgets/video.php:159 includes/widgets/video.php:184
#: includes/widgets/video.php:208 includes/widgets/video.php:268
msgid "Enter your URL"
msgstr "URL خود را وارد کنید"

#: includes/widgets/inner-section.php:35 assets/js/editor.js:28108
msgid "Inner Section"
msgstr "بخش داخلی"

#: includes/widgets/video.php:457
msgid "Lazy Load"
msgstr "بارگذاری آهسته"

#: includes/settings/admin-menu-items/getting-started-menu-item.php:36
msgid "Create Your First Page"
msgstr "صفحه اول خود را ایجاد کنید"

#: includes/editor-templates/navigator.php:102
msgid "Easy Navigation is Here!"
msgstr "ناوبری آسان در اینجاست!"

#: includes/editor-templates/navigator.php:97
msgid "Empty"
msgstr "خالی"

#: includes/editor-templates/hotkeys.php:126
#: includes/editor-templates/navigator.php:38
#: includes/editor-templates/panel.php:87
#: includes/editor-templates/panel.php:91 assets/js/editor.js:31846
msgid "Navigator"
msgstr "ناوبر"

#: includes/editor-templates/library-layout.php:13
#: includes/settings/admin-menu-items/getting-started-menu-item.php:55
#: includes/settings/admin-menu-items/getting-started-menu-item.php:56
#: modules/announcements/module.php:122 assets/js/app-packages.js:2837
#: assets/js/app.js:3271
#: assets/js/onboarding.120a44527e5a7209a8e4.bundle.js:1268
#: assets/js/onboarding.120a44527e5a7209a8e4.bundle.js:1487
#: assets/js/onboarding.120a44527e5a7209a8e4.bundle.js:1579
#: assets/js/onboarding.120a44527e5a7209a8e4.bundle.js:1822
#: assets/js/onboarding.120a44527e5a7209a8e4.bundle.js:1997
#: assets/js/onboarding.120a44527e5a7209a8e4.bundle.js:2318
msgid "Skip"
msgstr "پرش"

#: includes/controls/url.php:68
#: modules/atomic-widgets/controls/types/link-control.php:23
#: modules/atomic-widgets/elements/atomic-image/atomic-image.php:53
#: modules/atomic-widgets/elements/atomic-svg/atomic-svg.php:53
#: modules/atomic-widgets/elements/div-block/div-block.php:83
#: modules/floating-buttons/base/widget-contact-button-base.php:949
#: modules/floating-buttons/base/widget-floating-bars-base.php:166
#: modules/floating-buttons/base/widget-floating-bars-base.php:352
#: modules/link-in-bio/base/widget-link-in-bio-base.php:270
#: modules/shapes/widgets/text-path.php:164
msgid "Paste URL or type"
msgstr "URL را وارد کنید یا تایپ کنید"

#: core/debug/inspector.php:49
msgid "Debug Bar"
msgstr "نوار اشکال زدایی"

#: includes/controls/groups/css-filter.php:129
msgctxt "Filter Control"
msgid "Hue"
msgstr "رنگ"

#: includes/settings/admin-menu-items/getting-started-menu-item.php:39
msgid "Create Your First Post"
msgstr "اولین پست خود را ایجاد کنید"

#: includes/editor-templates/navigator.php:103
msgid "Once you fill your page with content, this window will give you an overview display of all the page elements. This way, you can easily move around any section, column, or widget."
msgstr "هنگامی که صفحه خود را با محتوای خود پر می کنید، این پنجره به شما یک نمای کلی از تمام عناصر صفحه را می دهد. به این ترتیب شما می توانید بخش ها، ستون ها و ویدجت های مختلف را به راحتی حرکت دهید."

#: includes/settings/admin-menu-items/getting-started-menu-item.php:23
#: includes/settings/admin-menu-items/getting-started-menu-item.php:27
#: includes/settings/admin-menu-items/getting-started-menu-item.php:52
#: assets/js/onboarding.120a44527e5a7209a8e4.bundle.js:792
msgid "Getting Started"
msgstr "شروع به کار"

#: includes/widgets/accordion.php:171 includes/widgets/accordion.php:175
#: includes/widgets/icon-box.php:186 includes/widgets/image-box.php:161
#: includes/widgets/tabs.php:170 includes/widgets/tabs.php:174
#: includes/widgets/testimonial.php:124 includes/widgets/text-editor.php:142
#: includes/widgets/toggle.php:174 includes/widgets/toggle.php:178
msgid "Lorem ipsum dolor sit amet, consectetur adipiscing elit. Ut elit tellus, luctus nec ullamcorper mattis, pulvinar dapibus leo."
msgstr "برای تغییر این متن بر روی دکمه ویرایش کلیک کنید. لورم ایپسوم متن ساختگی با تولید سادگی نامفهوم از صنعت چاپ و با استفاده از طراحان گرافیک است."

#: core/admin/admin-notices.php:280
msgid "You created over 10 pages with Elementor. Great job! If you can spare a minute, please help us by leaving a five star review on WordPress.org."
msgstr "شما بیش از ۱۰ برگه با المنتور ایجاد کردید. کارت عالی بود! اگر می‌توانید یک دقیقه وقت بگذارید، لطفا با گذاشتن یک بررسی پنج ستاره در WordPress.org به ما کمک کنید."

#: core/kits/documents/tabs/settings-lightbox.php:76 includes/frontend.php:1414
#: includes/widgets/google-maps.php:174 assets/js/ai-admin.js:10232
#: assets/js/ai-admin.js:10235 assets/js/ai-gutenberg.js:12080
#: assets/js/ai-gutenberg.js:12083 assets/js/ai-media-library.js:11861
#: assets/js/ai-media-library.js:11864
#: assets/js/ai-unify-product-images.js:11861
#: assets/js/ai-unify-product-images.js:11864 assets/js/ai.js:13308
#: assets/js/ai.js:13311
msgid "Zoom"
msgstr "بزرگنمایی"

#: core/debug/inspector.php:57
msgid "Debug Bar adds an admin bar menu that lists all the templates that are used on a page that is being displayed."
msgstr "نوار اشکال زدایی یک منوی نوار مدیریتی را اضافه می کند که تمام قالب هایی را که در یک صفحه نمایش داده می شوند، لیست می کند."

#: core/document-types/page-base.php:47
msgid "Single"
msgstr "تکی"

#: includes/widgets/video.php:429
msgid "Logo"
msgstr "لوگو"

#: includes/widgets/video.php:402
msgid "Video Info"
msgstr "اطلاعات ویدیو"

#: includes/widgets/video.php:133
msgid "Source"
msgstr "منبع"

#: includes/widgets/traits/button-trait.php:205
msgid "Button ID"
msgstr "شناسه دکمه"

#: includes/widgets/audio.php:193
msgid "Artwork"
msgstr "اثر هنری"

#: includes/controls/groups/css-filter.php:113
msgctxt "Filter Control"
msgid "Saturation"
msgstr "اشباع رنگ"

#: includes/controls/groups/css-filter.php:97
msgctxt "Filter Control"
msgid "Contrast"
msgstr "کنتراست"

#: core/debug/inspector.php:115
msgid "Elementor Debugger"
msgstr "دیباگرِ المنتور"

#: includes/controls/groups/background.php:561 includes/widgets/video.php:317
msgid "End Time"
msgstr "زمان پایان"

#: app/modules/site-editor/module.php:32
#: core/common/modules/finder/categories/site.php:26 core/kits/manager.php:437
#: includes/managers/elements.php:310 modules/admin-bar/module.php:149
msgid "Site"
msgstr "سایت"

#: core/admin/feedback.php:105
msgid "I have Elementor Pro"
msgstr "من لایسنس اصلی افزونه را دارم"

#: core/admin/feedback.php:107
msgid "Wait! Don't deactivate Elementor. You have to activate both Elementor and Elementor Pro in order for the plugin to work."
msgstr "صبر کنید ! افزونه را غیرفعال نکنید. جهت استفاده از این افزونه، هر دو افزونه Elementor و Elementor Pro باید فعال باشد."

#: includes/controls/groups/background.php:549 includes/widgets/video.php:306
msgid "Start Time"
msgstr "زمان آغاز"

#: includes/controls/groups/background.php:551 includes/widgets/video.php:308
msgid "Specify a start time (in seconds)"
msgstr "زمان آغاز را بر حسب ثانیه مشخص کنید"

#: includes/controls/groups/background.php:563 includes/widgets/video.php:319
msgid "Specify an end time (in seconds)"
msgstr "زمان پایان را بر حسب ثانیه مشخص کنید"

#: includes/controls/groups/css-filter.php:62
msgctxt "Filter Control"
msgid "Blur"
msgstr "تاری"

#: includes/controls/groups/css-filter.php:81
msgctxt "Filter Control"
msgid "Brightness"
msgstr "درخشندگی"

#: includes/editor-templates/global.php:49
#: assets/js/cf70912a0f34653ad242.bundle.js:130
msgid "Drag widget here"
msgstr "ابزارک را به اینجا بکشید و رها کنید"

#: includes/editor-templates/hotkeys.php:41 assets/js/editor.js:30731
msgid "Copy"
msgstr "کپی"

#: includes/elements/column.php:441 includes/elements/container.php:842
#: includes/elements/section.php:715 includes/widgets/heading.php:309
msgid "Blend Mode"
msgstr "حالت ادغام"

#: includes/widgets/video.php:139
msgid "Dailymotion"
msgstr "دیلی موشن"

#: includes/widgets/video.php:141
msgid "Self Hosted"
msgstr "از هاست خودتان"

#: core/base/providers/social-network-provider.php:216
#: includes/widgets/video.php:255 includes/widgets/video.php:279
msgid "URL"
msgstr "آدرس URL"

#: includes/managers/elements.php:292
#: modules/promotions/widgets/pro-widget-promotion.php:66
#: assets/js/ai-admin.js:7993 assets/js/ai-gutenberg.js:9841
#: assets/js/ai-layout.js:3474 assets/js/ai-media-library.js:9622
#: assets/js/ai-unify-product-images.js:9622 assets/js/ai.js:11069
#: assets/js/kit-library.b0f0ab89c95fe1f6fde3.bundle.js:3180
msgid "Pro"
msgstr "پرو"

#: includes/widgets/traits/button-trait.php:216
msgid "Please make sure the ID is unique and not used elsewhere on the page this form is displayed. This field allows %1$sA-z 0-9%2$s & underscore chars without spaces."
msgstr "لطفاً مطمئن شوید که شناسه منحصر به فرد است و در جای دیگری از صفحه نمایش این فرم استفاده نشده است. این فیلد به %1$sA-z 0-9%2$s و نویسه‌های زیرخط بدون فاصله اجازه می‌دهد."

#: includes/managers/elements.php:317
msgid "WooCommerce"
msgstr "ویجت‌های ووکامرس"

#: core/admin/admin.php:219 assets/js/admin.js:2058 assets/js/gutenberg.js:147
msgid "Back to WordPress Editor"
msgstr "بازگشت به ویرایشگر وردپرس"

#. translators: %s: Document title.
#: core/documents-manager.php:388
msgid "Elementor %s"
msgstr "المنتور %s"

#. translators: %s: Document title.
#. translators: %s: Template type label.
#: core/base/document.php:267
#: core/common/modules/finder/categories/create.php:86
#: core/document-types/page-base.php:183
#: includes/template-library/sources/local.php:1402
msgid "Add New %s"
msgstr "افزودن %s جدید"

#: core/kits/documents/tabs/theme-style-images.php:95
#: core/kits/documents/tabs/theme-style-images.php:166
#: includes/elements/column.php:410 includes/elements/column.php:484
#: includes/elements/container.php:796 includes/elements/container.php:910
#: includes/elements/section.php:669 includes/elements/section.php:773
#: includes/widgets/image-box.php:429 includes/widgets/image-box.php:464
#: includes/widgets/image.php:441 includes/widgets/image.php:475
#: modules/floating-buttons/base/widget-floating-bars-base.php:1010
#: modules/link-in-bio/base/widget-link-in-bio-base.php:1499
msgid "Opacity"
msgstr "شفافیت"

#: includes/widgets/image.php:319
msgid "Max Width"
msgstr "حداکثر عرض"

#: includes/template-library/sources/local.php:232
#: assets/js/new-template.js:147
msgid "New Template"
msgstr "قالب جدید"

#: includes/controls/groups/background.php:265
#: includes/controls/groups/background.php:313
#: includes/controls/groups/box-shadow.php:69
#: includes/elements/container.php:1522 includes/widgets/common-base.php:444
#: includes/widgets/common-base.php:1091 includes/widgets/divider.php:766
#: includes/widgets/divider.php:932 includes/widgets/image-carousel.php:572
#: includes/widgets/image-carousel.php:636 includes/widgets/tabs.php:184
#: includes/widgets/traits/button-trait.php:251
#: modules/floating-buttons/base/widget-floating-bars-base.php:448
#: modules/link-in-bio/base/widget-link-in-bio-base.php:940
#: modules/link-in-bio/base/widget-link-in-bio-base.php:995
#: modules/nested-accordion/widgets/nested-accordion.php:213
#: modules/nested-tabs/widgets/nested-tabs.php:857
#: assets/js/packages/editor-controls/editor-controls.js:8
#: assets/js/packages/editor-controls/editor-controls.js:10
#: assets/js/packages/editor-editing-panel/editor-editing-panel.js:8
#: assets/js/packages/editor-editing-panel/editor-editing-panel.strings.js:5
#: assets/js/packages/editor-editing-panel/editor-editing-panel.strings.js:80
msgid "Position"
msgstr "جایگاه"

#. translators: 1: Elementor, 2: Link to plugin review
#: core/admin/admin.php:408
msgid "Enjoyed %1$s? Please leave us a %2$s rating. We really appreciate your support!"
msgstr "لذت بردید%1$s?لطفا به ما امتیاز a %2$s دهید.از پشتیبانی شما سپاسگذاریم!"

#: core/common/modules/finder/categories/general.php:67
msgid "Knowledge Base"
msgstr "پایگاه مقالات"

#: modules/page-templates/module.php:297
msgid "Page Layout"
msgstr "طرح بندی صفحه"

#: modules/page-templates/module.php:340
msgid "No header, no footer, just Elementor"
msgstr "بدون سربرگ، بدون پابرگ، فقط المنتور"

#: modules/page-templates/module.php:352
msgid "This template includes the header, full-width content and footer"
msgstr "این قالب شامل سربرگ، پابرگ و محتوای تمام عرض است"

#: modules/page-templates/module.php:328
msgid "Default Page Template from your theme."
msgstr "طرح پیش فرض صفحه از قالب شما"

#: includes/widgets/counter.php:210
msgid "Separator"
msgstr "جداکننده"

#: includes/template-library/sources/local.php:1267
msgid "All"
msgstr "همه"

#: includes/admin-templates/new-template.php:115
msgid "Create Template"
msgstr "ایجاد قالب"

#. translators: %s: Document title.
#. translators: %s: Post type label.
#: core/base/document.php:1223 core/settings/page/model.php:127
#: includes/editor-templates/panel.php:80
#: assets/js/packages/editor-app-bar/editor-app-bar.js:2
#: assets/js/packages/editor-app-bar/editor-app-bar.strings.js:32
msgid "%s Settings"
msgstr "تنظیمات %s"

#: includes/widgets/common-base.php:233 includes/widgets/icon-list.php:126
#: includes/widgets/icon-list.php:217
msgid "Inline"
msgstr "در متن"

#: includes/admin-templates/new-floating-elements.php:44
#: includes/admin-templates/new-template.php:109
msgid "Name your template"
msgstr "برای قالب خود نامی انتخاب کنید"

#: includes/admin-templates/new-template.php:66
msgid "Select the type of template you want to work on"
msgstr "نوع قالب را مشخص کنید"

#: includes/admin-templates/new-template.php:64
msgid "Choose Template Type"
msgstr "انتخاب نوع قالب"

#: core/common/modules/ajax/module.php:131
msgid "Token Expired."
msgstr "توکن منقضی شده است."

#: core/common/modules/ajax/module.php:165
msgid "Action not found."
msgstr "عملیاتی یافت نشد."

#: core/base/document.php:257
msgid "Document"
msgstr "داکیومنت"

#: core/document-types/page-base.php:124
msgid "Body Style"
msgstr "استایل بدنه"

#: core/document-types/page-base.php:230
msgid "Featured Image"
msgstr "تصویر شاخص"

#: core/dynamic-tags/tag.php:115 includes/settings/settings.php:389
msgid "Fallback"
msgstr "جایگزین"

#: core/common/modules/finder/categories/general.php:61
#: core/role-manager/role-manager-menu-item.php:28
#: core/role-manager/role-manager-menu-item.php:32
#: core/role-manager/role-manager.php:50
msgid "Role Manager"
msgstr "مدیریت نقش‌ها"

#: core/role-manager/role-manager.php:150
msgid "Role Excluded"
msgstr "نقش‌های نادیده گرفته شده"

#: core/role-manager/role-manager.php:157
msgid "No access to editor"
msgstr "بدون دسترسی به ویرایشگر"

#: includes/editor-templates/global.php:120
msgid "This tag has no settings."
msgstr "این برچسب هیچ تنظیماتی ندارد."

#: core/document-types/page.php:72 modules/library/documents/page.php:61
#: assets/js/app.js:11125 assets/js/editor.js:9996
#: assets/js/kit-library.b0f0ab89c95fe1f6fde3.bundle.js:2191
#: assets/js/packages/editor-site-navigation/editor-site-navigation.js:2
#: assets/js/packages/editor-site-navigation/editor-site-navigation.strings.js:1
#: assets/js/packages/editor-site-navigation/editor-site-navigation.strings.js:3
#: assets/js/packages/editor-site-navigation/editor-site-navigation.strings.js:7
msgid "Pages"
msgstr "برگه‌ها"

#: includes/editor-templates/templates.php:233
msgid "More actions"
msgstr "عملیات بیشتر"

#: includes/admin-templates/new-floating-elements.php:47
#: includes/admin-templates/new-template.php:112
msgid "Enter template name (optional)"
msgstr "درج نام قالب (اختیاری)"

#. translators: %s: Template type label.
#: includes/template-library/sources/local.php:1395
msgid "Create Your First %s"
msgstr "ایجاد اولین %s شما"

#: includes/frontend.php:1416 includes/widgets/video.php:987
msgid "Play Video"
msgstr "پخش ویدیو"

#: core/document-types/page-base.php:182
#: includes/template-library/sources/admin-menu-items/add-new-template-menu-item.php:23
#: modules/landing-pages/module.php:284 assets/js/app-packages.js:4592
#: assets/js/packages/editor-site-navigation/editor-site-navigation.js:2
#: assets/js/packages/editor-site-navigation/editor-site-navigation.strings.js:8
msgid "Add New"
msgstr "افزودن جدید"

#: includes/template-library/sources/local.php:1362
msgid "Add templates and reuse them across your website. Easily export and import them to any other project, for an optimized workflow."
msgstr "قالب ها را می توانید به صورت چند باره در وبسایت خود به کار گیرید. حتی می توانید آنها را برون بری کرده و در وبسایت های دیگر نیز استفاده کنید."

#: core/role-manager/role-manager.php:114
msgid "Manage What Your Users Can Edit In Elementor"
msgstr "مشخص کنید کاربران شما می توانند چه مطالبی را با المنتور ویرایش کنند"

#: core/role-manager/role-manager.php:239
msgid "Want to give access only to content?"
msgstr "آیا می خواهید فقط به محتوا دسترسی داشته باشند؟"

#: core/kits/documents/tabs/global-typography.php:182
#: modules/promotions/admin-menu-items/custom-fonts-promotion-item.php:15
#: modules/promotions/admin-menu-items/custom-fonts-promotion-item.php:19
#: assets/js/packages/editor-controls/editor-controls.js:10
#: assets/js/styleguide-app.a6e297c616479b98c03d.bundle.js:235
msgid "Custom Fonts"
msgstr "فونت‌های سفارشی"

#: includes/template-library/sources/local.php:227
msgctxt "Template Library"
msgid "My Templates"
msgstr "قالب‌های من"

#: includes/editor-templates/templates.php:130
msgid "Search Templates:"
msgstr "جستجوی قالب‌ها:"

#: includes/admin-templates/new-template.php:57
msgid "Use templates to create the different pieces of your site, and reuse them with one click whenever needed."
msgstr "از قالب‌ها برای ایجاد بخش‌های مختلف سایت خود استفاده کنید و هر زمان که نیاز بود با یک کلیک مجددا از آنها استفاده کنید."

#: includes/widgets/image-carousel.php:183
msgid "Set how many slides are scrolled per swipe."
msgstr "مشخص کنید چه تعداد اسلاید در هر چرخش اسکرول شوند."

#: core/admin/admin.php:477
msgid "Create New Post"
msgstr "ایجاد نوشته جدید"

#: includes/controls/groups/background.php:448
msgid "Note: Attachment Fixed works only on desktop."
msgstr "توجه: حالت های ضمیمه و ثابت فقط در نسخه دسکتاپ به کار می روند."

#: includes/fonts.php:77
msgid "Google (Early Access)"
msgstr "گوگل (دسترسی اولیه)"

#: includes/widgets/alert.php:137
msgid "This is an Alert"
msgstr "این یک هشدار است"

#: core/kits/documents/kit.php:155
#: modules/history/views/revisions-panel-template.php:64
msgid "Published"
msgstr "منتشر شده"

#: includes/editor-templates/templates.php:192
msgid "Favorite"
msgstr "مورد علاقه"

#: includes/editor-templates/templates.php:326
#: includes/editor-templates/templates.php:342
#: includes/editor-templates/templates.php:358
#: includes/widgets/traits/button-trait.php:57
#: modules/atomic-widgets/elements/atomic-button/atomic-button.php:46
#: assets/js/app.js:6856 assets/js/app.js:7861
#: assets/js/onboarding.120a44527e5a7209a8e4.bundle.js:2525
msgid "Click here"
msgstr "اینجا کلیک کنید"

#. translators: %s: Document title.
#: core/base/document.php:198
msgid "Hurray! Your %s is live."
msgstr "هورا ! %s شما آنلاین است."

#: core/base/document.php:1534
msgctxt "revision date format"
msgid "M j, H:i"
msgstr "M j, H:i"

#: core/document-types/page-base.php:215
msgid "Excerpt"
msgstr "چکیده"

#: core/admin/admin.php:428
msgid "Elementor Overview"
msgstr "نمای کلی المنتور"

#: core/admin/admin.php:474
msgid "Create New Page"
msgstr "ایجاد برگه جدید"

#: core/admin/admin.php:524
msgid "Recently Edited"
msgstr "به تازگی ویرایش شده"

#: core/admin/admin.php:532
msgctxt "Dashboard Overview Widget Recently Date"
msgid "M jS"
msgstr "M jS"

#: core/admin/admin.php:597
msgid "(opens in a new window)"
msgstr "(باز کردن در پنجره جدید)"

#: core/admin/admin.php:617
msgid "Blog"
msgstr "بلاگ"

#: includes/controls/dimensions.php:148
msgid "Unlinked values"
msgstr "عدم اتصال مقادیر"

#: includes/controls/groups/typography.php:185
msgctxt "Typography Control"
msgid "Decoration"
msgstr "دکوراسیون"

#: includes/controls/groups/typography.php:190
msgctxt "Typography Control"
msgid "Underline"
msgstr "زیرخط دار"

#: includes/controls/groups/typography.php:191
msgctxt "Typography Control"
msgid "Overline"
msgstr "رو خط دار"

#: includes/controls/groups/typography.php:192
msgctxt "Typography Control"
msgid "Line Through"
msgstr "میان خط دار"

#: core/experiments/manager.php:547 includes/controls/popover-toggle.php:71
#: includes/controls/popover-toggle.php:73
msgid "Back to default"
msgstr "بازگردانی پیشفرض"

#: includes/editor-templates/panel-elements.php:73
msgid "Search Widget:"
msgstr "جستجوی ابزارک ها:"

#: core/base/document.php:171 includes/editor-templates/panel.php:114
#: assets/js/editor.js:25780
#: assets/js/packages/editor-app-bar/editor-app-bar.js:12
#: assets/js/packages/editor-app-bar/editor-app-bar.strings.js:28
msgid "Publish"
msgstr "انتشار"

#: includes/editor-templates/panel.php:133
#: assets/js/packages/editor-app-bar/editor-app-bar.js:12
#: assets/js/packages/editor-app-bar/editor-app-bar.strings.js:24
msgid "Save Draft"
msgstr "ذخیره پیش نویس"

#: includes/editor-templates/panel.php:137 assets/js/editor.js:33405
#: assets/js/editor.js:33901
#: assets/js/packages/editor-app-bar/editor-app-bar.js:12
#: assets/js/packages/editor-app-bar/editor-app-bar.strings.js:23
msgid "Save as Template"
msgstr "ذخیره قالب"

#: includes/editor-templates/panel.php:151
#: includes/editor-templates/panel.php:153 assets/js/editor.js:36599
msgid "Hide Panel"
msgstr "مخفی سازی پنل"

#: includes/editor-templates/repeater.php:12
msgid "Drag & Drop"
msgstr "درگ اند دراپ"

#: includes/editor-templates/hotkeys.php:73
#: includes/editor-templates/repeater.php:18 assets/js/editor.js:30716
#: assets/js/editor.js:51301
#: assets/js/packages/editor-site-navigation/editor-site-navigation.js:2
#: assets/js/packages/editor-site-navigation/editor-site-navigation.strings.js:16
msgid "Duplicate"
msgstr "تکثیر"

#: core/kits/views/panel.php:40 includes/controls/icons.php:83
#: includes/controls/icons.php:85 includes/controls/media.php:215
#: includes/controls/media.php:217 includes/controls/media.php:279
#: includes/controls/media.php:281 includes/editor-templates/repeater.php:23
#: modules/promotions/widgets/pro-widget-promotion.php:74
#: assets/js/ai-admin.js:2311 assets/js/ai-admin.js:7480
#: assets/js/ai-gutenberg.js:4079 assets/js/ai-gutenberg.js:9328
#: assets/js/ai-layout.js:2961 assets/js/ai-media-library.js:3940
#: assets/js/ai-media-library.js:9109 assets/js/ai-unify-product-images.js:3940
#: assets/js/ai-unify-product-images.js:9109 assets/js/ai.js:4719
#: assets/js/ai.js:10556
#: assets/js/kit-library.b0f0ab89c95fe1f6fde3.bundle.js:919
msgid "Remove"
msgstr "حذف"

#: includes/editor-templates/templates.php:13
#: includes/editor-templates/templates.php:14
msgid "Import Template"
msgstr "درون‌ریزی قالب"

#: includes/editor-templates/templates.php:79
msgid "Trend"
msgstr "پر مصرف"

#: includes/editor-templates/templates.php:81
#: assets/js/kit-library.b0f0ab89c95fe1f6fde3.bundle.js:3811
msgid "Popular"
msgstr "محبوب"

#: includes/editor-templates/templates.php:103
msgid "My Favorites"
msgstr "مورد علاقه من"

#: includes/editor-templates/templates.php:148
msgid "Created By"
msgstr "ایجاد شده توسط"

#: includes/editor-templates/templates.php:152
msgid "Creation Date"
msgstr "تاریخ ایجاد"

#: includes/editor-templates/templates.php:325
#: includes/editor-templates/templates.php:341
#: includes/editor-templates/templates.php:357
msgid "Want to learn more about the Elementor library?"
msgstr "می خواهید در مورد کتابخانه المنتور بیشتر بدانید؟"

#: includes/editor-templates/templates.php:335
msgid "Import Template to Your Library"
msgstr "درون ریزی قالب به کتابخانه شما"

#: includes/editor-templates/templates.php:336
msgid "Drag & drop your .JSON or .zip template file"
msgstr "فایل قالب zip یا json را بکشید و در اینجا رها کنید"

#: includes/editor-templates/templates.php:337
#: assets/js/onboarding.120a44527e5a7209a8e4.bundle.js:2169
#: assets/js/onboarding.120a44527e5a7209a8e4.bundle.js:2513
msgid "or"
msgstr "یا"

#: includes/editor-templates/templates.php:338 assets/js/app-packages.js:2528
#: assets/js/app.js:2962
msgid "Select File"
msgstr "انتخاب فایل"

#: includes/widgets/accordion.php:213 includes/widgets/toggle.php:216
#: modules/nested-tabs/widgets/nested-tabs.php:137
msgid "Active Icon"
msgstr "آیکن فعال"

#: includes/widgets/alert.php:150 includes/widgets/icon-box.php:187
#: includes/widgets/image-box.php:162
msgid "Enter your description"
msgstr "توضیح خود را وارد کنید"

#: includes/widgets/image.php:178
msgid "Enter your image caption"
msgstr "عنوان تصویر را وارد کنید"

#: includes/widgets/shortcode.php:110
msgid "Enter your shortcode"
msgstr "کد کوتاه را وارد کنید"

#: includes/controls/groups/background.php:597 includes/widgets/video.php:444
msgid "Privacy Mode"
msgstr "حالت حریم خصوصی"

#: modules/history/revisions-manager.php:157
msgid "Current Version"
msgstr "نسخه کنونی"

#: includes/editor-templates/panel.php:119
#: assets/js/packages/editor-app-bar/editor-app-bar.js:12
#: assets/js/packages/editor-app-bar/editor-app-bar.strings.js:26
#: assets/js/packages/editor-app-bar/editor-app-bar.strings.js:27
msgid "Save Options"
msgstr "تنظیمات ذخیره سازی"

#: includes/widgets/html.php:107
msgid "Enter your code"
msgstr "کد خود را وارد کنید"

#. translators: 1: Editing date, 2: Author display name.
#: core/base/document.php:1547
msgid "Last edited on %1$s by %2$s"
msgstr "در %1$s توسط %2$s ویرایش شده است"

#: includes/widgets/heading.php:170
msgid "Add Your Heading Text Here"
msgstr "متن سربرگ خود را وارد کنید"

#: includes/editor-templates/templates.php:77
#: assets/js/kit-library.b0f0ab89c95fe1f6fde3.bundle.js:3807
msgid "New"
msgstr "جدید"

#: core/common/modules/finder/template.php:19 assets/js/editor.js:11519
#: assets/js/kit-library.b0f0ab89c95fe1f6fde3.bundle.js:1879
msgid "No Results Found"
msgstr "نتیجه ای یافت نشد"

#: includes/editor-templates/templates.php:131
#: assets/js/packages/editor-controls/editor-controls.js:10
msgid "Search"
msgstr "جستجو"

#: includes/widgets/video.php:446
msgid "When you turn on privacy mode, YouTube/Vimeo won't store information about visitors on your website unless they play the video."
msgstr "وقتی حالت حریم خصوصی را روشن می‌کنید، یوتیوب/ویمیو اطلاعات بازدیدکنندگان را در وب‌سایت شما ذخیره نمی‌کند مگر اینکه آنها ویدیو را پخش کنند."

#: core/admin/admin.php:565
msgid "News & Updates"
msgstr "اخبار و بروزرسانی‌ها"

#: includes/editor-templates/panel.php:102
#: assets/js/packages/editor-app-bar/editor-app-bar.js:2
#: assets/js/packages/editor-app-bar/editor-app-bar.strings.js:30
msgid "Preview Changes"
msgstr "پیشنمایش تغییرات"

#. translators: 1: Saving date, 2: Author display name.
#: core/base/document.php:1540
msgid "Draft saved on %1$s by %2$s"
msgstr "در %1$s توسط %2$s پیشنویس شده است."

#: includes/template-library/sources/local.php:496
#: includes/template-library/sources/local.php:612
#: includes/template-library/sources/local.php:762
msgid "Access denied."
msgstr "دسترسی ممکن نیست."

#: includes/settings/settings.php:288
msgid "Disable Default Fonts"
msgstr "غیرفعال کردن فونت‌های پیشفرض"

#: includes/controls/groups/flex-container.php:105
#: includes/controls/groups/flex-container.php:141
#: includes/controls/groups/flex-container.php:218
#: includes/controls/groups/flex-item.php:63
#: includes/controls/groups/flex-item.php:89
#: includes/controls/groups/grid-container.php:143
#: includes/controls/groups/grid-container.php:171
#: includes/controls/groups/grid-container.php:200
#: includes/controls/groups/grid-container.php:240
#: includes/elements/column.php:217 includes/widgets/accordion.php:427
#: includes/widgets/common-base.php:416 includes/widgets/counter.php:292
#: includes/widgets/counter.php:326 includes/widgets/counter.php:400
#: includes/widgets/counter.php:436 includes/widgets/icon-list.php:585
#: includes/widgets/image-carousel.php:744 includes/widgets/rating.php:211
#: includes/widgets/tabs.php:217 includes/widgets/tabs.php:247
#: includes/widgets/toggle.php:451 includes/widgets/traits/button-trait.php:151
#: includes/widgets/traits/button-trait.php:295
#: modules/floating-buttons/base/widget-floating-bars-base.php:586
#: modules/floating-buttons/base/widget-floating-bars-base.php:1197
#: modules/floating-buttons/base/widget-floating-bars-base.php:1296
#: modules/nested-accordion/widgets/nested-accordion.php:181
#: modules/nested-accordion/widgets/nested-accordion.php:221
#: modules/nested-tabs/widgets/nested-tabs.php:242
#: modules/nested-tabs/widgets/nested-tabs.php:284
#: modules/nested-tabs/widgets/nested-tabs.php:354
#: assets/js/packages/editor-editing-panel/editor-editing-panel.js:8
#: assets/js/packages/editor-editing-panel/editor-editing-panel.strings.js:92
#: assets/js/packages/editor-editing-panel/editor-editing-panel.strings.js:122
#: assets/js/packages/editor-editing-panel/editor-editing-panel.strings.js:127
msgid "End"
msgstr "پایان"

#: includes/controls/groups/flex-container.php:97
#: includes/controls/groups/flex-container.php:133
#: includes/controls/groups/flex-container.php:210
#: includes/controls/groups/flex-item.php:55
#: includes/controls/groups/flex-item.php:85
#: includes/controls/groups/grid-container.php:135
#: includes/controls/groups/grid-container.php:163
#: includes/controls/groups/grid-container.php:192
#: includes/controls/groups/grid-container.php:232
#: includes/elements/column.php:215 includes/widgets/accordion.php:423
#: includes/widgets/common-base.php:408 includes/widgets/counter.php:288
#: includes/widgets/counter.php:318 includes/widgets/counter.php:392
#: includes/widgets/counter.php:428 includes/widgets/icon-list.php:577
#: includes/widgets/image-carousel.php:736 includes/widgets/rating.php:203
#: includes/widgets/tabs.php:209 includes/widgets/tabs.php:239
#: includes/widgets/toggle.php:447 includes/widgets/traits/button-trait.php:147
#: includes/widgets/traits/button-trait.php:287
#: modules/floating-buttons/base/widget-floating-bars-base.php:582
#: modules/floating-buttons/base/widget-floating-bars-base.php:1189
#: modules/floating-buttons/base/widget-floating-bars-base.php:1292
#: modules/nested-accordion/widgets/nested-accordion.php:173
#: modules/nested-accordion/widgets/nested-accordion.php:217
#: modules/nested-tabs/widgets/nested-tabs.php:234
#: modules/nested-tabs/widgets/nested-tabs.php:276
#: modules/nested-tabs/widgets/nested-tabs.php:346
#: assets/js/packages/editor-editing-panel/editor-editing-panel.js:8
#: assets/js/packages/editor-editing-panel/editor-editing-panel.strings.js:90
#: assets/js/packages/editor-editing-panel/editor-editing-panel.strings.js:120
#: assets/js/packages/editor-editing-panel/editor-editing-panel.strings.js:125
msgid "Start"
msgstr "آغاز"

#: core/debug/classes/inspection-base.php:25
#: core/debug/loading-inspection-manager.php:44
msgid "The preview could not be loaded"
msgstr "پیشنمایش قادر به بارگذاری نیست"

#: core/debug/loading-inspection-manager.php:43
msgid "We’re sorry, but something went wrong. Click on 'Learn more' and follow each of the steps to quickly solve it."
msgstr "متاسفیم! مشکلی به وجود آمده است. بر روی «کسب اطلاعات بیشتر» کلیک کرده و مراحل را گام به گام پیش روید تا مشکل برطرف شود."

#: core/admin/admin-notices.php:149 core/admin/admin-notices.php:184
msgid "Update Notification"
msgstr "اعلان بروزرسانی"

#. Author URI of the plugin
#: elementor.php
msgid "https://elementor.com/?utm_source=wp-plugins&utm_campaign=author-uri&utm_medium=wp-dash"
msgstr "https://elementor.com/?utm_source=wp-plugins&utm_campaign=author-uri&utm_medium=wp-dash"

#. Plugin URI of the plugin
#: elementor.php
msgid "https://elementor.com/?utm_source=wp-plugins&utm_campaign=plugin-uri&utm_medium=wp-dash"
msgstr "https://elementor.com/?utm_source=wp-plugins&utm_campaign=plugin-uri&utm_medium=wp-dash"

#: includes/editor-templates/hotkeys.php:144
#: includes/editor-templates/panel.php:96 assets/js/ai-admin.js:2060
#: assets/js/ai-gutenberg.js:3828 assets/js/ai-media-library.js:3689
#: assets/js/ai-unify-product-images.js:3689 assets/js/ai.js:4468
#: assets/js/editor.js:51623
#: assets/js/packages/editor-app-bar/editor-app-bar.js:12
#: assets/js/packages/editor-app-bar/editor-app-bar.strings.js:17
msgid "History"
msgstr "تاریخچه"

#: core/kits/documents/tabs/settings-lightbox.php:141
#: includes/widgets/video.php:893
msgid "UI Color"
msgstr "رنگ رابط کاربری"

#: core/kits/documents/tabs/settings-lightbox.php:152
#: includes/widgets/video.php:905
msgid "UI Hover Color"
msgstr "رنگ هاور رابط کاربری"

#: includes/editor-templates/hotkeys.php:19
#: includes/editor-templates/templates.php:155
#: modules/history/views/history-panel-template.php:9 assets/js/editor.js:51031
msgid "Actions"
msgstr "عملیات"

#: modules/history/views/history-panel-template.php:10
#: assets/js/editor.js:51034
msgid "Revisions"
msgstr "بازنگری‌ها"

#: modules/history/views/history-panel-template.php:24
msgid "No History Yet"
msgstr "هنوز تاریخچه ای وجود ندارد"

#: modules/history/views/history-panel-template.php:25
msgid "Once you start working, you'll be able to redo / undo any action you make in the editor."
msgstr "به محض آغاز فرآیند طراحی، می توانید هر عملیاتی را redo / undo کنید."

#: modules/history/views/history-panel-template.php:17
msgid "Switch to Revisions tab for older versions"
msgstr "جهت مشاهده نسخه های قدیمی تر به زبانه بازنگری‌ها بروید"

#: core/kits/documents/tabs/settings-lightbox.php:45
msgid "Image Lightbox"
msgstr "لایت باکس تصاویر"

#: includes/widgets/video.php:366
msgid "Mute"
msgstr "بی‌صدا"

#: includes/template-library/sources/local.php:1004
msgid "Choose an Elementor template JSON file or a .zip archive of Elementor templates, and add them to the list of templates available in your library."
msgstr "فایل JSON یا ZIP قالب های المنتور را انتخاب و درون ریزی کنید تا به فهرست قالب های شما افزوده شوند."

#: core/kits/documents/tabs/settings-lightbox.php:48
msgid "Open all image links in a lightbox popup window. The lightbox will automatically work on any link that leads to an image file."
msgstr "با فعالسازی این گزینه، هنگامی که بر روی تصویر کلیک کنید، آن تصویر در یک لایت باکس باز می شود."

#: core/kits/documents/tabs/settings-layout.php:101
#: includes/elements/column.php:231
msgid "Widgets Space"
msgstr "فضای بین ابزارک ها"

#: includes/rollback.php:165 includes/settings/tools.php:193
#: includes/settings/tools.php:368 assets/js/admin.js:2252
msgid "Rollback to Previous Version"
msgstr "بازگشت به نسخه قبل"

#: includes/controls/url.php:112
msgid "Open in new window"
msgstr "باز کردن در پنجره جدید"

#: includes/controls/url.php:116
msgid "Add nofollow"
msgstr "افزودن نو فالو"

#. Translators: %s: Element Name.
#. Translators: %s: Element name.
#. translators: %s: Element type title.
#: core/document-types/page-base.php:184 assets/js/atomic-widgets-editor.js:970
#: assets/js/editor.js:30517 assets/js/editor.js:30703
#: assets/js/editor.js:32987 assets/js/editor.js:33453
#: assets/js/editor.js:33554 assets/js/editor.js:33880
#: assets/js/editor.js:37193 assets/js/editor.js:48131
#: assets/js/packages/editor-editing-panel/editor-editing-panel.js:8
#: assets/js/packages/editor-editing-panel/editor-editing-panel.strings.js:11
msgid "Edit %s"
msgstr "ویرایش %s"

#: core/kits/documents/tabs/theme-style-form-fields.php:137
#: core/kits/documents/tabs/theme-style-images.php:203
#: includes/base/element-base.php:1254
#: includes/controls/groups/background.php:673 includes/elements/column.php:355
#: includes/elements/column.php:515 includes/elements/column.php:623
#: includes/elements/container.php:719 includes/elements/container.php:933
#: includes/elements/container.php:1093 includes/elements/section.php:617
#: includes/elements/section.php:804 includes/elements/section.php:911
#: includes/widgets/alert.php:446 includes/widgets/common-base.php:823
#: includes/widgets/common-base.php:938 includes/widgets/google-maps.php:252
#: includes/widgets/heading.php:385 includes/widgets/icon-list.php:471
#: includes/widgets/icon-list.php:698 includes/widgets/image-box.php:482
#: includes/widgets/image.php:501 includes/widgets/text-editor.php:389
#: includes/widgets/traits/button-trait.php:450
#: modules/floating-buttons/base/widget-contact-button-base.php:1492
#: modules/floating-buttons/base/widget-contact-button-base.php:2231
#: modules/nested-tabs/widgets/nested-tabs.php:609
#: modules/shapes/widgets/text-path.php:460
#: modules/shapes/widgets/text-path.php:638
msgid "Transition Duration"
msgstr "مدت زمان جابجایی"

#: includes/elements/column.php:791 includes/elements/container.php:1746
#: includes/elements/section.php:1264 includes/widgets/common-base.php:663
#: assets/js/packages/editor-editing-panel/editor-editing-panel.js:8
#: assets/js/packages/editor-editing-panel/editor-editing-panel.strings.js:74
msgid "Z-Index"
msgstr "ایندکس Z"

#: includes/elements/column.php:900 includes/elements/container.php:1842
#: includes/elements/section.php:1353 includes/widgets/common-base.php:757
#: modules/floating-buttons/base/widget-contact-button-base.php:1415
#: modules/floating-buttons/base/widget-floating-bars-base.php:916
msgid "Animation Delay"
msgstr "تاخیر انیمیشن"

#: core/common/modules/finder/categories/settings.php:54
#: includes/settings/settings.php:304
#: assets/js/packages/editor-app-bar/editor-app-bar.js:2
#: assets/js/packages/editor-app-bar/editor-app-bar.strings.js:5
msgid "Integrations"
msgstr "یکپارچه سازی"

#: includes/settings/settings.php:341
msgid "Switch Editor Loader Method"
msgstr "تغییر روش بارگذاری ویرایشگر"

#: core/common/modules/finder/categories/tools.php:77
#: includes/settings/tools.php:365
msgid "Version Control"
msgstr "کنترل نسخه"

#: includes/settings/tools.php:381
msgid "Rollback Version"
msgstr "بازگشت نسخه رایگان"

#: includes/settings/tools.php:389
msgid "Warning: Please backup your database before making the rollback."
msgstr "هشدار: لطفا پیش از بازگشت به نسخه قبل از دیتابیس خود بکاپ تهیه کنید."

#: includes/settings/tools.php:396
msgid "Become a Beta Tester"
msgstr "ثبت نام برای تست نسخه بتا"

#: includes/settings/tools.php:412
msgid "Beta Tester"
msgstr "تست نسخه بتا"

#. translators: %s: Elementor version.
#: includes/settings/tools.php:372
msgid "Experiencing an issue with Elementor version %s? Rollback to a previous version before the issue appeared."
msgstr "با نسخه %s افزونه المنتور مشکلی دارید؟ به آخرین نسخه ای که در آن مشکلی نداشتید، باز گردید."

#: includes/controls/url.php:103
msgid "Link Options"
msgstr "گزینه های پیوند"

#: includes/settings/tools.php:420
msgid "Please Note: We do not recommend updating to a beta version on production sites."
msgstr "لطفا توجه داشته باشید که نسخه بتای افزونه برای سایت هایی که در حال سرویس دهی هستند، اکیدا توصیه نمی شود."

#: includes/settings/tools.php:399
msgid "Turn-on Beta Tester, to get notified when a new beta version of Elementor or Elementor Pro is available. The Beta version will not install automatically. You always have the option to ignore it."
msgstr "با ثبت نام برای تست نسخه بتا، به محض انتشار نسخه بتای جدیدی از المنتور یا المنتور حرفه ای به شما اطلاع رسانی می شود. نسخه بتا به صورت خودکار نصب نمی شود. همواره گزینه نادیده گرفتن آن در اختیار شماست."

#: core/kits/documents/tabs/settings-layout.php:118
msgid "Sets the default space between widgets (Default: 20px)"
msgstr "فضای بین ابزارک‌ها را تنظیم می‌کند (پیشفرض: ۲۰ پیکسل)."

#: includes/controls/groups/box-shadow.php:72
msgctxt "Box Shadow Control"
msgid "Outline"
msgstr "خارج خط"

#: includes/settings/settings.php:412
msgid "CSS Print Method"
msgstr "روش پرینت CSS"

#: includes/settings/settings.php:418
msgid "External File"
msgstr "فایل خارجی"

#: includes/settings/settings.php:419
msgid "Internal Embedding"
msgstr "جایگذاری درونی"

#: core/base/document.php:1992 modules/ai/preferences.php:67
#: assets/js/element-manager-admin.js:2282
#: assets/js/element-manager-admin.js:2359
msgid "Status"
msgstr "وضعیت"

#: core/debug/inspector.php:54 includes/settings/settings.php:346
#: includes/settings/settings.php:358 includes/settings/settings.php:371
#: includes/settings/settings.php:435 includes/settings/settings.php:452
#: includes/settings/settings.php:464 includes/settings/tools.php:417
#: modules/element-cache/module.php:133 modules/generator-tag/module.php:82
#: modules/nested-tabs/widgets/nested-tabs.php:381
#: modules/safe-mode/module.php:47
msgid "Disable"
msgstr "غیرفعال"

#: core/debug/inspector.php:55 includes/settings/settings.php:347
#: includes/settings/settings.php:359 includes/settings/settings.php:370
#: includes/settings/settings.php:434 includes/settings/settings.php:451
#: includes/settings/settings.php:463 includes/settings/tools.php:418
#: modules/generator-tag/module.php:81
#: modules/nested-tabs/widgets/nested-tabs.php:382
#: modules/safe-mode/module.php:48 assets/js/admin.js:294
#: assets/js/ai-admin.js:64 assets/js/ai-gutenberg.js:1693
#: assets/js/ai-media-library.js:1693 assets/js/ai-unify-product-images.js:1693
#: assets/js/ai.js:1693 assets/js/app-packages.js:2833 assets/js/app.js:3267
#: assets/js/common.js:2146 assets/js/editor.js:40031
msgid "Enable"
msgstr "فعال‌سازی"

#: includes/settings/settings.php:349
msgid "For troubleshooting server configuration conflicts."
msgstr "فقط در مورد عیب یابی به کار گرفته شود."

#: includes/admin-templates/new-template.php:75
#: includes/settings/controls.php:155
msgid "Select"
msgstr "انتخاب"

#: core/common/modules/finder/categories/edit.php:118 assets/js/editor.js:19424
msgid "Template"
msgstr "قالب"

#: core/settings/editor-preferences/model.php:108
msgid "Canvas"
msgstr "بوم"

#: includes/maintenance-mode.php:251
msgid "Choose Template"
msgstr "انتخاب قالب"

#: includes/maintenance-mode.php:292 includes/maintenance-mode.php:369
#: includes/template-library/sources/local.php:231 assets/js/app.js:9600
msgid "Edit Template"
msgstr "ویرایش قالب"

#: includes/maintenance-mode.php:237
msgid "Logged In"
msgstr "وارد شده"

#: core/kits/documents/kit.php:154 includes/maintenance-mode.php:215
#: assets/js/editor.js:51300
msgid "Disabled"
msgstr "غیرفعال شده"

#: includes/maintenance-mode.php:210
msgid "Choose Mode"
msgstr "انتخاب حالت"

#: core/document-types/page-base.php:96
msgid "Hide Title"
msgstr "مخفی سازی عنوان"

#: includes/editor-templates/hotkeys.php:57
msgid "Paste Style"
msgstr "پیست استایل"

#: core/common/modules/finder/categories/tools.php:62
#: includes/maintenance-mode.php:201 includes/maintenance-mode.php:205
msgid "Maintenance Mode"
msgstr "حالت تعمیر"

#: includes/maintenance-mode.php:216
msgid "Coming Soon"
msgstr "به زودی"

#: includes/maintenance-mode.php:217
msgid "Maintenance"
msgstr "حالت تعمیر"

#: includes/maintenance-mode.php:220
msgid "Choose between Coming Soon mode (returning HTTP 200 code) or Maintenance Mode (returning HTTP 503 code)."
msgstr "بین حالت به زودی (نمایش کد ۲۰۰) یا حالت تعمیر (نمایش کد ۵۰۳) یکی را انتخاب کنید."

#: includes/widgets/common-base.php:1196 includes/widgets/spacer.php:130
#: includes/widgets/text-editor.php:503
msgid "Space"
msgstr "فضای خالی"

#: includes/widgets/text-editor.php:148 includes/widgets/text-editor.php:410
msgid "Drop Cap"
msgstr "حرف اول بزرگ"

#: includes/maintenance-mode.php:226
msgid "Coming Soon returns HTTP 200 code, meaning the site is ready to be indexed."
msgstr "حالت به زودی، خطای HTTP کد ۲۰۰ می دهد. در این حالت موتورهای جستجو سایت شما را ایندکس می کنند."

#: core/kits/documents/tabs/settings-layout.php:143
msgid "Elementor lets you hide the page title. This works for themes that have \"h1.entry-title\" selector. If your theme's selector is different, please enter it above."
msgstr "المنتور به شما اجازه می دهد تا عنوان برگه را مخفی کنید. این ویژگی برای پوسته هایی که سلکتور h1.entry-title دارند کار می کند. اگر پوسته شما از سلکتور متفاوتی استفاده می کند، آن را مشخص کنید."

#: includes/maintenance-mode.php:283
msgid "Maintenance Mode ON"
msgstr "حالت تعمیر فعال است"

#: includes/maintenance-mode.php:223
msgid "Maintenance Mode returns HTTP 503 code, so search engines know to come back a short time later. It is not recommended to use this mode for more than a couple of days."
msgstr "حالت تعمیر، خطای HTTP کد ۵۰۳ می دهد. در این حالت موتورهای جستجو می فهمند که دوباره به سایت بازگردند. این حالت برای بیشتر از ۲-۳ روز توصیه نمی شود."

#: core/kits/documents/tabs/settings-layout.php:139
msgid "Page Title Selector"
msgstr "سلکتور عنوان برگه"

#: includes/maintenance-mode.php:243
msgid "Roles"
msgstr "نقش های کاربری"

#: includes/maintenance-mode.php:206
msgid "Set your entire website as MAINTENANCE MODE, meaning the site is offline temporarily for maintenance, or set it as COMING SOON mode, meaning the site is offline until it is ready to be launched."
msgstr "قرار دادن وبسایت در حالت تعمیر به این معنی است که سایت شما برای مدت کوتاهی جهت تعمیر و نگهداری، آفلاین است. در حالت به زودی، سایت شما در زمان مقرر، راه اندازی می شود."

#: includes/maintenance-mode.php:370
msgid "To enable maintenance mode you have to set a template for the maintenance mode page."
msgstr "جهت فعالسازی حالت تعمیر می بایست یک قالب برای برگه در دست تعمیر تعریف کنید."

#: includes/maintenance-mode.php:231
msgid "Who Can Access"
msgstr "چه کسی می تواند به سایت دسترسی داشته باشد"

#: includes/elements/container.php:1318 includes/elements/section.php:1115
msgid "Bring to Front"
msgstr "جلو آوردن"

#: includes/shapes.php:208
msgctxt "Shapes"
msgid "Split"
msgstr "جدا کننده"

#: includes/shapes.php:191
msgctxt "Shapes"
msgid "Waves"
msgstr "موج ها"

#: includes/shapes.php:161
msgctxt "Shapes"
msgid "Triangle"
msgstr "مثلث"

#: includes/shapes.php:147
msgctxt "Shapes"
msgid "Clouds"
msgstr "ابرها"

#: includes/shapes.php:141
msgctxt "Shapes"
msgid "Drops"
msgstr "قطره ها"

#: includes/shapes.php:137
msgctxt "Shapes"
msgid "Mountains"
msgstr "کوه ها"

#: includes/elements/container.php:1305 includes/elements/section.php:1102
msgid "Invert"
msgstr "معکوس"

#: core/kits/documents/tabs/settings-lightbox.php:18
#: includes/widgets/image-carousel.php:366
#: includes/widgets/image-gallery.php:200 includes/widgets/image.php:225
#: includes/widgets/video.php:721 includes/widgets/video.php:869
msgid "Lightbox"
msgstr "لایت باکس"

#: includes/elements/container.php:1159 includes/elements/section.php:956
msgid "Shape Divider"
msgstr "جدا کننده"

#: includes/elements/container.php:1291 includes/elements/section.php:1088
msgid "Flip"
msgstr "فلیپ"

#: includes/shapes.php:153 includes/widgets/divider.php:189
#: includes/widgets/divider.php:319
msgctxt "Shapes"
msgid "Zigzag"
msgstr "زیگ زاگ"

#: includes/shapes.php:156
msgctxt "Shapes"
msgid "Pyramids"
msgstr "هرم ها"

#: includes/shapes.php:165
msgctxt "Shapes"
msgid "Triangle Asymmetrical"
msgstr "مثلث نامتقارن"

#: includes/shapes.php:170
msgctxt "Shapes"
msgid "Tilt"
msgstr "شیب"

#: includes/shapes.php:179
msgctxt "Shapes"
msgid "Fan Opacity"
msgstr "شفافیت پروانه"

#: includes/shapes.php:182
msgctxt "Shapes"
msgid "Curve"
msgstr "خم"

#: includes/shapes.php:186
msgctxt "Shapes"
msgid "Curve Asymmetrical"
msgstr "خم نامتقارن"

#: includes/shapes.php:196
msgctxt "Shapes"
msgid "Waves Brush"
msgstr "براش موج"

#: includes/shapes.php:200
msgctxt "Shapes"
msgid "Waves Pattern"
msgstr "الگوی موج"

#: includes/shapes.php:204
msgctxt "Shapes"
msgid "Arrow"
msgstr "پیکان"

#: includes/shapes.php:212
msgctxt "Shapes"
msgid "Book"
msgstr "کتابی"

#: includes/widgets/icon-list.php:230
msgid "List"
msgstr "ليست"

#: includes/controls/groups/flex-container.php:109
#: includes/controls/groups/flex-container.php:222
#: includes/controls/groups/grid-container.php:204
#: includes/controls/groups/grid-container.php:244
#: includes/elements/column.php:190 includes/elements/column.php:218
#: includes/elements/section.php:428 includes/widgets/icon-list.php:238
#: includes/widgets/toggle.php:317
msgid "Space Between"
msgstr "فاصله بینابینی"

#: includes/shapes.php:175
msgctxt "Shapes"
msgid "Tilt Opacity"
msgstr "شیب شفاف"

#: core/kits/documents/tabs/theme-style-buttons.php:166
#: core/kits/documents/tabs/theme-style-images.php:134
#: core/kits/documents/tabs/theme-style-typography.php:148
#: includes/base/element-base.php:873 includes/elements/column.php:340
#: includes/elements/column.php:469 includes/elements/column.php:588
#: includes/elements/container.php:704 includes/elements/container.php:885
#: includes/elements/container.php:1046 includes/elements/section.php:602
#: includes/elements/section.php:758 includes/elements/section.php:876
#: includes/widgets/alert.php:429 includes/widgets/common-base.php:808
#: includes/widgets/common-base.php:903 includes/widgets/google-maps.php:237
#: includes/widgets/heading.php:367 includes/widgets/icon-box.php:433
#: includes/widgets/icon-list.php:451 includes/widgets/icon-list.php:679
#: includes/widgets/icon.php:256 includes/widgets/image-box.php:449
#: includes/widgets/image.php:468 includes/widgets/text-editor.php:371
#: includes/widgets/traits/button-trait.php:392
#: modules/floating-buttons/base/widget-contact-button-base.php:1251
#: modules/floating-buttons/base/widget-contact-button-base.php:2010
#: modules/floating-buttons/base/widget-contact-button-base.php:2492
#: modules/floating-buttons/base/widget-contact-button-base.php:2672
#: modules/floating-buttons/base/widget-floating-bars-base.php:690
#: modules/floating-buttons/base/widget-floating-bars-base.php:1394
#: modules/nested-accordion/widgets/nested-accordion.php:663
#: modules/nested-accordion/widgets/nested-accordion.php:726
#: modules/nested-tabs/widgets/nested-tabs.php:545
#: modules/nested-tabs/widgets/nested-tabs.php:773
#: modules/nested-tabs/widgets/nested-tabs.php:954
#: modules/shapes/widgets/text-path.php:433
#: modules/shapes/widgets/text-path.php:572
msgid "Hover"
msgstr "هاور"

#: includes/elements/column.php:803 includes/elements/container.php:1758
#: includes/elements/section.php:1276 includes/widgets/common-base.php:674
#: modules/floating-buttons/base/widget-contact-button-base.php:3088
#: modules/floating-buttons/base/widget-floating-bars-base.php:1496
#: modules/nested-accordion/widgets/nested-accordion.php:129
#: modules/nested-tabs/widgets/nested-tabs.php:151
msgid "CSS ID"
msgstr "شناسه CSS"

#: includes/elements/column.php:812 includes/elements/container.php:1767
#: includes/elements/section.php:1285 includes/widgets/common-base.php:683
#: includes/widgets/traits/button-trait.php:214
#: modules/floating-buttons/base/widget-contact-button-base.php:3097
#: modules/floating-buttons/base/widget-floating-bars-base.php:1505
#: modules/nested-accordion/widgets/nested-accordion.php:138
#: modules/nested-tabs/widgets/nested-tabs.php:160
msgid "Add your custom id WITHOUT the Pound key. e.g: my-id"
msgstr "شناسه CSS خود را بدون # وارد کنید. مثال: my-id"

#: includes/widgets/tabs.php:275
msgid "Navigation Width"
msgstr "عرض ناوبری"

#: includes/controls/groups/background.php:230
msgctxt "Background Control"
msgid "Type"
msgstr "نوع"

#: includes/controls/groups/background.php:184
#: includes/controls/groups/background.php:213
msgctxt "Background Control"
msgid "Location"
msgstr "موقعیت"

#: core/admin/admin.php:377
msgid "View Elementor Documentation"
msgstr "مشاهده مستندات المنتور"

#: core/admin/admin.php:377
msgid "Docs & FAQs"
msgstr "مستندات و سوالات متداول"

#: core/admin/admin.php:378
msgid "Video Tutorials"
msgstr "آموزش‌های تصویری"

#: includes/settings/settings.php:284
msgid "Checking this box will disable Elementor's Default Colors, and make Elementor inherit the colors from your theme."
msgstr "با علامت زدن این گزینه، رنگ های پیشفرض المنتور غیرفعال می شوند و المنتور رنگ های خود را از پوسته شما می گیرد."

#: includes/settings/settings.php:292
msgid "Checking this box will disable Elementor's Default Fonts, and make Elementor inherit the fonts from your theme."
msgstr "با علامت زدن این گزینه، فونت های پیشفرض المنتور غیرفعال می شوند و المنتور فونت های خود را از پوسته شما می گیرد."

#: core/admin/admin.php:378
msgid "View Elementor Video Tutorials"
msgstr "مشاهده آموزش های ویدیویی المنتور"

#. translators: 1: Human readable time difference, 2: Date.
#: modules/history/revisions-manager.php:179
msgid "%1$s ago (%2$s)"
msgstr "%1$s قبل (%2$s)"

#: modules/history/revisions-manager.php:160
msgid "Autosave"
msgstr "ذخیره خودکار"

#: modules/apps/admin-apps-page.php:177
#: modules/history/views/revisions-panel-template.php:55
msgid "By"
msgstr "توسط"

#: core/common/modules/finder/categories/tools.php:56
#: includes/settings/tools.php:334 includes/settings/tools.php:338
#: includes/settings/tools.php:355
msgid "Replace URL"
msgstr "جایگزینی URL"

#: modules/history/revisions-manager.php:151
msgctxt "revision date format"
msgid "M j @ H:i"
msgstr "M j @ H:i"

#: modules/history/revisions-manager.php:163
msgid "Revision"
msgstr "بازنگری"

#: modules/history/views/revisions-panel-template.php:27
msgid "Start designing your page and you will be able to see the entire revision history here."
msgstr "طراحی صفحه خود را شروع کنید و می توانید کل تاریخچه ویرایش را در اینجا مشاهده کنید."

#: includes/settings/tools.php:356
msgid "Enter your old and new URLs for your WordPress installation, to update all Elementor data (Relevant for domain transfers or move to 'HTTPS')."
msgstr "جهت بروزرسانی داده های المنتور، آدرس محل نصب جدید و قدیم وردپرس را وارد کنید (برای زمان انتقال دامنه و یا فعالسازی https مفید است)."

#: modules/history/views/revisions-panel-template.php:28
msgid "It looks like the post revision feature is unavailable in your website."
msgstr "به نظر می آید ویژگی بازنگری مطالب در سایت شما غیرفعال شده است."

#: modules/history/views/revisions-panel-template.php:39
msgid "No Revisions Saved Yet"
msgstr "هنوز تاریخچه ای ذخیره نشده است"

#: modules/history/views/revisions-panel-template.php:26
msgid "Revision history lets you save your previous versions of your work, and restore them any time."
msgstr "تاریخچه بازنگری ها به شما این امکان را می دهد تا تاریخچه ای از تغییرات کار خود را ذخیره و در هر زمان آنها را بازگردانی کنید."

#: includes/settings/tools.php:352
msgid "Update Site Address (URL)"
msgstr "بروزرسانی آدرس سایت (URL)"

#. translators: 1: Minimum recommended_memory, 2: Preferred memory, 3:
#. WordPress wp-config memory documentation.
#: modules/system-info/reporters/server.php:170
msgid "We recommend setting memory to at least %1$s. (%2$s or higher is preferred) For more information, read about <a href=\"%3$s\">how to increase memory allocated to PHP</a>."
msgstr "توصیه می‌کنیم حافظه را حداقل روی %1$s تنظیم کنید. (%2$s یا بالاتر توصیه می‌شود) برای اطلاعات بیشتر، درباره <a href=\"%3$s\">نحوه افزایش حافظه اختصاص داده شده به PHP</a> بخوانید."

#: includes/widgets/counter.php:199
msgid "Thousand Separator"
msgstr "جداکننده هزارگان"

#: includes/base/element-base.php:917 includes/base/element-base.php:1081
#: includes/widgets/common-base.php:982 includes/widgets/icon-list.php:285
#: includes/widgets/icon.php:329 includes/widgets/text-editor.php:150
#: includes/widgets/video.php:724 modules/shapes/widgets/text-path.php:220
msgid "Off"
msgstr "خاموش"

#: modules/promotions/widgets/pro-widget-promotion.php:75
#: assets/js/ai-admin.js:7924 assets/js/ai-gutenberg.js:9772
#: assets/js/ai-layout.js:3405 assets/js/ai-media-library.js:9553
#: assets/js/ai-unify-product-images.js:9553 assets/js/ai.js:11000
msgid "Go Pro"
msgstr "تغییر به نسخه حرفه ای"

#: core/kits/documents/tabs/settings-custom-css.php:17
#: includes/managers/controls.php:1075
msgid "Custom CSS"
msgstr "CSS سفارشی"

#: includes/managers/controls.php:1093
msgid "Meet Our Custom CSS"
msgstr "کدهای CSS سفارشی ما را مشاهده کنید"

#: includes/base/element-base.php:916 includes/base/element-base.php:1080
#: includes/widgets/common-base.php:981 includes/widgets/icon-list.php:286
#: includes/widgets/icon.php:330 includes/widgets/text-editor.php:151
#: includes/widgets/video.php:725 modules/shapes/widgets/text-path.php:219
msgid "On"
msgstr "در"

#: includes/managers/controls.php:1081
msgid "Custom CSS lets you add CSS code to any widget, and see it render live right in the editor."
msgstr "کدهای CSS سفارشی به شما اجازه می دهند تا استایل های CSS را به صورت زنده بر روی ابزارک ها اعمال کنید و نتیجه آن را مشاهده نمایید."

#: includes/editor-templates/panel-elements.php:28
msgid "Get more with Elementor Pro"
msgstr "ابزارک های بیشتری را با نسخه حرفه ای تجربه کنید"

#: includes/editor-templates/panel-elements.php:95
msgid "Meet Our Global Widget"
msgstr "مشاهده ابزارک های عمومی ما"

#: includes/editor-templates/panel-elements.php:96
msgid "With this feature, you can save a widget as global, then add it to multiple areas. All areas will be editable from one single place."
msgstr "با استفاده از این ویژگی، شما می توانید یک ابزارک را به صورت عمومی ذخیره کنید و به این ترتیب برای وردپرس شبکه استفاده کنید. کلیه نواجی را می توان از یک قسمت مدیریت کرد."

#: includes/widgets/traits/button-trait.php:38
msgid "Extra Large"
msgstr "خیلی بزرگ"

#: includes/base/widget-base.php:312 includes/base/widget-base.php:321
msgid "Skin"
msgstr "پوسته"

#: includes/frontend.php:1259
msgid "Invalid Data: The Template ID cannot be the same as the currently edited template. Please choose a different one."
msgstr "داده نامعتبر: شناسه قالب نمی تواند مشابه شناسه قالبی باشد که در حال ویرایش است. لطفا شناسه دیگری انتخاب کنید."

#: includes/settings/settings.php:298
msgid "Improve Elementor"
msgstr "بهبود المنتور"

#: includes/widgets/traits/button-trait.php:34
msgid "Extra Small"
msgstr "بسیار کوچک"

#: includes/editor-templates/panel.php:209
msgid "%s are disabled"
msgstr "%s غیر فعال شده است"

#: includes/editor-templates/panel.php:173
msgid "Update changes to page"
msgstr "بروزرسانی تغییرات در برگه"

#: core/admin/admin-notices.php:245
msgid "No thanks"
msgstr "نه، ممنون"

#: core/kits/documents/tabs/settings-layout.php:158
msgid "Stretched Section Fit To"
msgstr "اندازه شدن بخش کشیده"

#: includes/elements/section.php:464
msgid "Stretch Section"
msgstr "بخش کشیده"

#: includes/elements/section.php:472
msgid "Stretch the section to the full width of the page using JS."
msgstr "اندازه بخش را برای هم عرض شدن با برگه کشیده کنید."

#: core/kits/documents/tabs/settings-layout.php:161
msgid "Enter parent element selector to which stretched sections will fit to (e.g. #primary / .wrapper / main etc). Leave blank to fit to page width."
msgstr "سلکتور المان والدی که بخش های کشیده شده باید نسبت به آن اندازه شوند را مشخص کنید (به عنوان مثال #primary / .wrapper / main و یا ...). برای اندازه شدن نسبت به عرض برگه، این قسمت را خالی بگذارید."

#: core/kits/documents/tabs/settings-layout.php:74
msgid "Sets the default width of the content area (Default: 1140px)"
msgstr "عرض پیش‌فرض ناحیه محتوا را تنظیم می‌کند (پیش‌فرض: 1140 پیکسل)"

#: core/settings/editor-preferences/model.php:124
#: assets/js/kit-library.b0f0ab89c95fe1f6fde3.bundle.js:4354
msgid "Mobile"
msgstr "موبایل"

#: includes/elements/section.php:1384
msgid "Reverse Columns"
msgstr "معکوس کردن ستون ها"

#: core/admin/admin-notices.php:233
msgid "Learn more."
msgstr "بیشتر بدانید."

#: includes/controls/dimensions.php:141 includes/controls/dimensions.php:144
msgid "Link values together"
msgstr "اتصال مقادیر به یکدیگر"

#: includes/widgets/shortcode.php:42 includes/widgets/shortcode.php:103
msgid "Shortcode"
msgstr "کد کوتاه"

#: includes/editor-templates/templates.php:41
#: assets/js/kit-library.b0f0ab89c95fe1f6fde3.bundle.js:1473
msgid "Back to Library"
msgstr "بازگشت به کتابخانه"

#: includes/editor-templates/templates.php:301
#: includes/editor-templates/templates.php:310
msgid "Enter Template Name"
msgstr "نام قالب را وارد کنید"

#: includes/template-library/sources/cloud.php:92
#: includes/template-library/sources/local.php:500
msgid "(no title)"
msgstr "(بدون عنوان)"

#: includes/editor-templates/global.php:45
msgid "Add Template"
msgstr "افزودن قالب"

#: core/base/document.php:170 includes/editor-templates/global.php:21
#: includes/editor-templates/responsive-bar.php:65 includes/frontend.php:1419
#: assets/js/ai-admin.js:588 assets/js/ai-gutenberg.js:2356
#: assets/js/ai-layout.js:420 assets/js/ai-media-library.js:2217
#: assets/js/ai-unify-product-images.js:2217 assets/js/ai.js:2996
#: assets/js/app-packages.js:2027 assets/js/app-packages.js:3994
#: assets/js/app-packages.js:4513 assets/js/app.js:2216 assets/js/app.js:4329
#: assets/js/app.js:4732 assets/js/app.js:6840 assets/js/app.js:7671
#: assets/js/app.js:11222 assets/js/cf70912a0f34653ad242.bundle.js:211
#: assets/js/cf70912a0f34653ad242.bundle.js:212 assets/js/editor.js:47442
#: assets/js/import-export-admin.js:313
#: assets/js/packages/editor-editing-panel/editor-editing-panel.strings.js:24
msgid "Close"
msgstr "بستن"

#: core/common/modules/connect/apps/library.php:16 assets/js/editor.js:10935
msgid "Library"
msgstr "کتابخانه"

#: core/common/modules/finder/categories/tools.php:28
#: core/common/modules/finder/categories/tools.php:50
#: includes/settings/admin-menu-items/tools-menu-item.php:29
#: includes/settings/admin-menu-items/tools-menu-item.php:33
#: includes/settings/tools.php:32 includes/settings/tools.php:33
#: includes/settings/tools.php:453
msgid "Tools"
msgstr "ابزارها"

#: includes/editor-templates/templates.php:18
#: includes/editor-templates/templates.php:19 includes/settings/tools.php:322
#: includes/settings/tools.php:325
msgid "Sync Library"
msgstr "همگام سازی کتابخانه"

#: includes/template-library/sources/local.php:209
msgid "Local"
msgstr "محلی"

#: includes/template-library/sources/local.php:228
msgctxt "Template Library"
msgid "Template"
msgstr "قالب"

#: core/document-types/page.php:58 modules/library/documents/page.php:57
#: assets/js/editor.js:10292
#: assets/js/packages/editor-site-navigation/editor-site-navigation.js:2
#: assets/js/packages/editor-site-navigation/editor-site-navigation.strings.js:2
msgid "Page"
msgstr "صفحه"

#: includes/editor-templates/templates.php:228
#: includes/editor-templates/templates.php:259
#: includes/editor-templates/templates.php:273 assets/js/ai-admin.js:6721
#: assets/js/ai-gutenberg.js:8569 assets/js/ai-media-library.js:8350
#: assets/js/ai-unify-product-images.js:8350 assets/js/ai.js:9797
#: assets/js/editor.js:8497
msgid "Insert"
msgstr "درج"

#: includes/editor-templates/templates.php:239
#: includes/template-library/sources/local.php:1189 assets/js/app.js:11321
msgid "Export"
msgstr "برون بری"

#: includes/template-library/sources/local.php:280
msgctxt "Template Library"
msgid "Type"
msgstr "نوع"

#: includes/template-library/sources/local.php:974
msgid "Export Template"
msgstr "برون بری قالب"

#: includes/template-library/sources/local.php:1011
msgid "Import Now"
msgstr "هم اکنون درون ریزی کن"

#: includes/template-library/sources/remote.php:61
msgid "Remote"
msgstr "ریموت"

#: includes/editor-templates/hotkeys.php:181
msgid "Template Library"
msgstr "کتابخانه‌ قالب‌ها"

#: includes/settings/tools.php:326
msgid "Elementor Library automatically updates on a daily basis. You can also manually update it by clicking on the sync button."
msgstr "کتابخانه‌ قالب های المنتور به صورت روزانه بروزرسانی می شود. همچنین شما می توانید با کلیک بر روی دکمه همگام سازی آن را به صورت دستی بروزرسانی کنید."

#: includes/template-library/sources/local.php:1002
msgid "Import Templates"
msgstr "درون‌ریزی قالب‌ها"

#: includes/editor-templates/templates.php:165
msgid "Stay tuned! More awesome templates coming real soon."
msgstr "به گوش باشید! به زودی قالب‌های جذاب بیشتری عرضه خواهد شد."

#: core/common/modules/finder/categories/general.php:49
#: includes/template-library/sources/admin-menu-items/saved-templates-menu-item.php:23
#: includes/template-library/sources/local.php:1735 assets/js/app.js:10342
msgid "Saved Templates"
msgstr "قالب‌های ذخیره شده"

#. translators: %s: WordPress child themes documentation.
#: modules/system-info/reporters/theme.php:207
msgid "If you want to modify the source code of your theme, we recommend using a <a href=\"%s\">child theme</a>."
msgstr "در صورتی که قصد ویرایش سورس کدهای پوسته خود را دارید پیشنهاد می‌کنیم از <a href=\"%s\">پوسته فرزند</a> استفاده کنید."

#: app/modules/import-export/module.php:152
#: app/modules/import-export/module.php:164 core/admin/admin-notices.php:332
#: modules/apps/admin-apps-page.php:187 modules/safe-mode/module.php:359
#: modules/safe-mode/module.php:368
#: modules/safe-mode/mu-plugin/elementor-safe-mode.php:105
#: assets/js/app-packages.js:2706 assets/js/app-packages.js:5662
#: assets/js/app-packages.js:5770 assets/js/app.js:3140 assets/js/app.js:7124
#: assets/js/app.js:8265 assets/js/app.js:9762 assets/js/app.js:10071
#: assets/js/app.js:10117 assets/js/app.js:11221 assets/js/editor.js:15135
#: assets/js/editor.js:28638 assets/js/editor.js:28669
#: assets/js/editor.js:40760 assets/js/element-manager-admin.js:2169
#: assets/js/kit-library.b0f0ab89c95fe1f6fde3.bundle.js:3842
msgid "Learn More"
msgstr "کسب اطلاعات بیشتر"

#: core/kits/documents/tabs/global-typography.php:28
#: core/kits/documents/tabs/global-typography.php:47 assets/js/app.js:10360
#: assets/js/editor.js:47193
#: assets/js/styleguide-app.a6e297c616479b98c03d.bundle.js:222
msgid "Global Fonts"
msgstr "فونت‌های سراسری"

#: modules/floating-buttons/base/widget-contact-button-base.php:2214
#: assets/js/ai-admin.js:3607 assets/js/ai-gutenberg.js:5375
#: assets/js/ai-media-library.js:5236 assets/js/ai-unify-product-images.js:5236
#: assets/js/ai.js:6048
msgid "Animation"
msgstr "انیمیشن"

#: core/base/traits/shared-widget-controls-trait.php:288
#: includes/widgets/icon-box.php:469 includes/widgets/icon.php:294
#: includes/widgets/image-box.php:503 includes/widgets/image.php:519
#: includes/widgets/social-icons.php:577
#: includes/widgets/traits/button-trait.php:465
#: modules/floating-buttons/base/widget-contact-button-base.php:1474
#: modules/floating-buttons/base/widget-contact-button-base.php:2510
#: modules/floating-buttons/base/widget-floating-bars-base.php:748
#: modules/nested-tabs/widgets/nested-tabs.php:601
#: modules/shapes/widgets/text-path.php:452
msgid "Hover Animation"
msgstr "انیمیشن هاور"

#: includes/elements/column.php:886 includes/elements/container.php:1828
#: includes/elements/section.php:1339 includes/widgets/common-base.php:743
#: modules/floating-buttons/base/widget-contact-button-base.php:1404
#: modules/floating-buttons/base/widget-contact-button-base.php:2787
#: modules/floating-buttons/base/widget-floating-bars-base.php:887
msgid "Slow"
msgstr "آهسته"

#: includes/elements/column.php:888 includes/elements/container.php:1830
#: includes/elements/section.php:1341 includes/widgets/common-base.php:745
#: modules/floating-buttons/base/widget-contact-button-base.php:1406
#: modules/floating-buttons/base/widget-contact-button-base.php:2789
#: modules/floating-buttons/base/widget-floating-bars-base.php:889
msgid "Fast"
msgstr "سریع"

#: includes/controls/box-shadow.php:73 includes/controls/text-shadow.php:76
#: includes/widgets/tabs.php:189
#: assets/js/packages/editor-controls/editor-controls.js:8
msgid "Vertical"
msgstr "عمودی"

#: includes/controls/box-shadow.php:68 includes/controls/text-shadow.php:71
#: includes/widgets/tabs.php:193
#: assets/js/packages/editor-controls/editor-controls.js:8
msgid "Horizontal"
msgstr "افقی"

#: includes/controls/box-shadow.php:83
#: assets/js/packages/editor-controls/editor-controls.js:8
msgid "Spread"
msgstr "گسترده"

#: includes/controls/box-shadow.php:78 includes/controls/text-shadow.php:66
#: assets/js/packages/editor-controls/editor-controls.js:8
msgid "Blur"
msgstr "محو شدگی"

#: includes/elements/column.php:873 includes/elements/container.php:1815
#: includes/elements/section.php:1326 includes/widgets/common-base.php:730
#: includes/widgets/video.php:917
#: modules/floating-buttons/base/widget-contact-button-base.php:1390
#: modules/floating-buttons/base/widget-floating-bars-base.php:873
msgid "Entrance Animation"
msgstr "انیمیشن هنگام ورود"

#: includes/settings/settings.php:280
msgid "Disable Default Colors"
msgstr "غیرفعال کردن رنگ های پیشفرض"

#: includes/controls/groups/box-shadow.php:73
msgctxt "Box Shadow Control"
msgid "Inset"
msgstr "داخل خط"

#: includes/widgets/social-icons.php:212 includes/widgets/social-icons.php:365
msgid "Official Color"
msgstr "رنگ رسمی"

#: includes/widgets/testimonial.php:201
msgid "Aside"
msgstr "ستون کناری"

#: includes/widgets/icon-box.php:152 includes/widgets/icon.php:150
#: includes/widgets/social-icons.php:285
#: modules/floating-buttons/base/widget-contact-button-base.php:2084
#: modules/floating-buttons/base/widget-contact-button-base.php:2175
#: modules/floating-buttons/base/widget-contact-button-base.php:2868
#: modules/floating-buttons/base/widget-floating-bars-base.php:845
#: modules/link-in-bio/base/widget-link-in-bio-base.php:119
msgid "Rounded"
msgstr "گرد"

#: includes/widgets/testimonial.php:46 includes/widgets/testimonial.php:111
msgid "Testimonial"
msgstr "دیدگاه مشتری"

#: includes/widgets/progress.php:123
msgid "My Skill"
msgstr "مهارت های من"

#: core/kits/documents/tabs/global-colors.php:123
#: assets/js/styleguide-app.a6e297c616479b98c03d.bundle.js:185
msgid "Custom Colors"
msgstr "رنگ‌های سفارشی"

#: includes/widgets/social-icons.php:42 includes/widgets/social-icons.php:107
#: includes/widgets/social-icons.php:250
msgid "Social Icons"
msgstr "آیکن‌های اجتماعی"

#: core/admin/feedback.php:94
msgid "Please share which plugin"
msgstr "لطفا به ما بگویید کدام افزونه است"

#: core/admin/feedback.php:97
msgid "I couldn't get the plugin to work"
msgstr "نمی توانم افزونه را به کار بیاندازم"

#: core/admin/feedback.php:101
msgid "It's a temporary deactivation"
msgstr "این یک غیرفعال سازی موقت است"

#: core/admin/feedback.php:110
msgid "Other"
msgstr "دیگر"

#: core/admin/feedback.php:119
msgid "Quick Feedback"
msgstr "بازخورد سریع"

#: includes/elements/section.php:290
msgid "Extended"
msgstr "باز شده"

#: includes/widgets/audio.php:130
msgid "Visual Player"
msgstr "پخش کننده ویژوال"

#: includes/widgets/audio.php:240
#: modules/floating-buttons/base/widget-contact-button-base.php:358
#: modules/floating-buttons/base/widget-contact-button-base.php:909
#: modules/link-in-bio/base/widget-link-in-bio-base.php:521
#: modules/link-in-bio/base/widget-link-in-bio-base.php:774
msgid "Username"
msgstr "نام کاربری"

#: includes/widgets/audio.php:229
msgid "Play Counts"
msgstr "تعداد نمایش"

#. translators: %s: Elementor version.
#: core/admin/admin-notices.php:140
msgid "View Elementor version %s details"
msgstr "مشاهده جزییات نسخه %s افزونه المنتور"

#: core/admin/feedback.php:111
msgid "Please share the reason"
msgstr "لطفا دلیل را به ما بگویید"

#: core/admin/feedback.php:127
msgid "If you have a moment, please share why you are deactivating Elementor:"
msgstr "اگر اندکی وقت دارید لطفا در خصوص دلیل غیرفعال کردن المنتور به ما بگویید:"

#: includes/elements/column.php:382 includes/elements/container.php:758
#: includes/elements/section.php:644
#: modules/floating-buttons/base/widget-floating-bars-base.php:981
#: modules/link-in-bio/base/widget-link-in-bio-base.php:1480
#: assets/js/packages/editor-controls/editor-controls.js:10
msgid "Background Overlay"
msgstr "روکش پس زمینه"

#: includes/widgets/audio.php:160
msgid "Buy Button"
msgstr "دکمه خرید"

#: includes/widgets/audio.php:171
msgid "Like Button"
msgstr "دکم لایک"

#: includes/widgets/audio.php:182 includes/widgets/video.php:561
msgid "Download Button"
msgstr "دکمه دانلود"

#: includes/widgets/audio.php:207
msgid "Share Button"
msgstr "دکمه اشتراک گذاری"

#: includes/widgets/audio.php:218
msgid "Comments"
msgstr "نظرات"

#: core/admin/feedback.php:93
msgid "I found a better plugin"
msgstr "یک افزونه بهتر پیدا کردم"

#: core/admin/feedback.php:89
msgid "I no longer need the plugin"
msgstr "از این پس به این افزونه نیازی ندارم"

#: core/admin/admin-notices.php:145 core/admin/admin-notices.php:153
#: core/base/db-upgrades-manager.php:97
msgid "Update Now"
msgstr "هم اکنون بروزرسانی کن"

#. translators: 1: Details URL, 2: Accessibility text, 3: Version number, 4:
#. Update URL, 5: Accessibility text.
#: core/admin/admin-notices.php:136
msgid "There is a new version of Elementor Page Builder available. <a href=\"%1$s\" class=\"thickbox open-plugin-details-modal\" aria-label=\"%2$s\">View version %3$s details</a> or <a href=\"%4$s\" class=\"update-link\" aria-label=\"%5$s\">update now</a>."
msgstr "نسخه جدیدی از صفحه ساز المنتور در دسترس است. <a href=\"%1$s\" class=\"thickbox open-plugin-details-modal\" aria-label=\"%2$s\">مشاهده جزییات نسخه %3$s</a> یا <a href=\"%4$s\" class=\"update-link\" aria-label=\"%5$s\">بروزرسانی افزونه</a>."

#: core/base/providers/social-network-provider.php:168
#: includes/widgets/audio.php:53 includes/widgets/audio.php:104
msgid "SoundCloud"
msgstr "SoundCloud"

#: core/kits/documents/tabs/settings-background.php:18
#: includes/elements/column.php:270 includes/elements/container.php:629
#: includes/elements/section.php:533 includes/widgets/accordion.php:326
#: includes/widgets/accordion.php:497 includes/widgets/common-base.php:781
#: includes/widgets/toggle.php:358 includes/widgets/toggle.php:521
#: modules/floating-buttons/base/widget-contact-button-base.php:1676
#: modules/floating-buttons/base/widget-floating-bars-base.php:952
#: modules/link-in-bio/base/widget-link-in-bio-base.php:1461
#: assets/js/ai-admin.js:11260 assets/js/ai-gutenberg.js:13108
#: assets/js/ai-media-library.js:12889
#: assets/js/ai-unify-product-images.js:12889 assets/js/ai.js:14336
#: assets/js/packages/editor-editing-panel/editor-editing-panel.js:8
#: assets/js/packages/editor-editing-panel/editor-editing-panel.strings.js:7
msgid "Background"
msgstr "پس زمینه"

#: includes/widgets/image-carousel.php:343 includes/widgets/image.php:198
msgid "Custom URL"
msgstr "آدرس سفارشی"

#: includes/elements/section.php:292
msgid "Wider"
msgstr "عریض‌ تر"

#: includes/editor-templates/hotkeys.php:49 assets/js/editor.js:30743
#: assets/js/editor.js:32916 assets/js/editor.js:42138
#: assets/js/editor.js:43172
msgid "Paste"
msgstr "پیست"

#: core/common/modules/finder/categories/general.php:29
#: core/role-manager/role-manager.php:69 includes/managers/elements.php:302
#: includes/settings/settings.php:252 includes/settings/settings.php:255
#: includes/settings/tools.php:309
#: assets/js/packages/editor-editing-panel/editor-editing-panel.js:8
#: assets/js/packages/editor-editing-panel/editor-editing-panel.strings.js:12
msgid "General"
msgstr "عمومی"

#: includes/widgets/image-carousel.php:523
msgid "Animation Speed"
msgstr "سرعت انیمیشن"

#: includes/elements/column.php:882 includes/elements/container.php:1824
#: includes/elements/section.php:1335 includes/widgets/common-base.php:739
#: includes/widgets/counter.php:188
#: modules/floating-buttons/base/widget-contact-button-base.php:1400
#: modules/floating-buttons/base/widget-contact-button-base.php:2783
#: modules/floating-buttons/base/widget-floating-bars-base.php:883
#: modules/nested-accordion/widgets/nested-accordion.php:367
msgid "Animation Duration"
msgstr "مدت انیمیشن"

#: includes/widgets/image-carousel.php:576
#: includes/widgets/image-carousel.php:641
msgid "Inside"
msgstr "درون"

#: includes/widgets/video.php:535
msgid "Intro Byline"
msgstr "معرفی خط فرعی"

#: includes/widgets/accordion.php:463 includes/widgets/divider.php:790
#: includes/widgets/divider.php:957 includes/widgets/image-carousel.php:760
#: includes/widgets/image-carousel.php:894
#: includes/widgets/image-gallery.php:440 includes/widgets/image.php:652
#: includes/widgets/rating.php:85 includes/widgets/social-icons.php:457
#: includes/widgets/star-rating.php:352 includes/widgets/toggle.php:487
#: modules/nested-accordion/widgets/nested-accordion.php:627
#: modules/nested-tabs/widgets/nested-tabs.php:915
#: assets/js/packages/editor-editing-panel/editor-editing-panel.js:8
#: assets/js/packages/editor-editing-panel/editor-editing-panel.strings.js:3
msgid "Spacing"
msgstr "فاصله‌گذاری"

#: includes/widgets/alert.php:240
msgid "Left Border Width"
msgstr "عرض حاشیه چپ"

#: includes/elements/container.php:533 includes/widgets/audio.php:143
#: includes/widgets/image-carousel.php:416
msgid "Additional Options"
msgstr "گزینه‌های اضافی"

#: includes/widgets/image-carousel.php:46
#: includes/widgets/image-carousel.php:125
#: includes/widgets/image-carousel.php:134
msgid "Image Carousel"
msgstr "کاروسل تصویر"

#: includes/widgets/image-carousel.php:198
msgid "Image Stretch"
msgstr "کشش تصویر"

#: includes/controls/groups/background.php:749
#: includes/controls/groups/flex-container.php:29
#: includes/widgets/image-carousel.php:534
#: modules/nested-tabs/widgets/nested-tabs.php:191
#: assets/js/packages/editor-editing-panel/editor-editing-panel.js:8
#: assets/js/packages/editor-editing-panel/editor-editing-panel.strings.js:45
#: assets/js/packages/editor-editing-panel/editor-editing-panel.strings.js:114
msgid "Direction"
msgstr "جهت‌یابی"

#: includes/widgets/image-carousel.php:577
#: includes/widgets/image-carousel.php:640
msgid "Outside"
msgstr "بیرون"

#: includes/widgets/image-carousel.php:215
msgid "Arrows and Dots"
msgstr "پیکان ها و نقطه ها"

#: includes/controls/groups/image-size.php:303
msgid "You can crop the original image size to any custom size. You can also set a single value for height or width in order to keep the original size ratio."
msgstr "شما می توانید ابعاد تصویر اصلی را در هر سایزی کراپ کنید. همچنین می توانید برای نگه داشتن نسبت ابعادی اولیه، یک عدد برای طول یا عرض تنظیم کنید."

#: includes/widgets/video.php:375
msgid "Loop"
msgstr "حلقه"

#: includes/widgets/video.php:507
msgid "Intro Title"
msgstr "عنوان معرفی"

#: includes/widgets/audio.php:251 includes/widgets/video.php:549
msgid "Controls Color"
msgstr "رنگ کنترل ها"

#: includes/widgets/video.php:521
msgid "Intro Portrait"
msgstr "پرتره معرفی"

#: includes/controls/groups/background.php:103 includes/widgets/video.php:45
#: includes/widgets/video.php:126 includes/widgets/video.php:739
msgid "Video"
msgstr "ویدیو"

#: core/base/providers/social-network-provider.php:186
#: includes/widgets/video.php:138
msgid "Vimeo"
msgstr "ویمئو"

#: includes/widgets/video.php:330
msgid "Video Options"
msgstr "تنظیمات ویدئو"

#: includes/controls/image-dimensions.php:81
msgid "The server does not have ImageMagick or GD installed and/or enabled! Any of these libraries are required for WordPress to be able to resize images. Please contact your server administrator to enable this before continuing."
msgstr "کتابخانه های ImageMagick و GD بر روی سرور نصب نشده یا فعال نیستند! وردپرس برای تغییر اندازه عکس ها به یکی از این کتابخانه ها نیاز دارد. لطفاً برای فعال کردن آنها با بخش پشتیبانی سرور خود تماس بگیرید."

#: includes/controls/groups/background.php:525
msgid "Video Link"
msgstr "پیوند Vimeo"

#: includes/controls/media.php:362
msgctxt "Image Size Control"
msgid "Full"
msgstr "اندازه‌ کامل"

#: includes/widgets/image-carousel.php:506
msgid "Effect"
msgstr "افکت"

#: includes/widgets/image-carousel.php:511
msgid "Fade"
msgstr "محو"

#: includes/controls/gallery.php:92
msgid "Edit gallery"
msgstr "ویرایش گالری"

#: includes/widgets/image-carousel.php:217
msgid "Dots"
msgstr "نقطه‌ها"

#: includes/controls/groups/background.php:634
#: includes/widgets/image-carousel.php:493
msgid "Infinite Loop"
msgstr "حلقه بی‌نهایت"

#: includes/controls/groups/background.php:107
msgid "Slideshow"
msgstr "اسلایدشو"

#: includes/elements/column.php:182 includes/widgets/icon-box.php:265
#: includes/widgets/icon-list.php:573 includes/widgets/image-box.php:240
msgid "Vertical Alignment"
msgstr "تراز عمودی"

#: includes/controls/groups/typography.php:321
msgctxt "Typography Control"
msgid "Width"
msgstr "عرض"

#: includes/elements/column.php:831 includes/elements/container.php:1786
#: includes/elements/section.php:1304 includes/widgets/common-base.php:701
#: modules/floating-buttons/base/widget-contact-button-base.php:3114
#: modules/floating-buttons/base/widget-floating-bars-base.php:1522
msgid "Add your custom class WITHOUT the dot. e.g: my-class"
msgstr "کلاس CSS خود را بدون نقطه وارد کنید. مثال: my-class"

#: includes/widgets/accordion.php:243 includes/widgets/counter.php:242
#: includes/widgets/icon-box.php:207 includes/widgets/image-box.php:182
#: includes/widgets/progress.php:131 includes/widgets/toggle.php:246
#: modules/nested-accordion/widgets/nested-accordion.php:270
msgid "Title HTML Tag"
msgstr "تگ HTML عنوان"

#: includes/widgets/icon-box.php:172 includes/widgets/image-box.php:147
msgid "This is the heading"
msgstr "این یک عنوان است"

#: core/kits/documents/tabs/global-typography.php:200
msgid "The list of fonts used if the chosen font is not available."
msgstr "فهرستی از فونت های پیشفرض که اگر فونت دلخواه شما در دسترس نباشد، به کار می روند."

#: includes/widgets/wordpress.php:242
msgid "Form"
msgstr "فرم"

#: includes/editor-templates/panel-elements.php:14 assets/js/editor.js:20060
#: assets/js/editor.js:21959 assets/js/editor.js:22368
#: assets/js/editor.js:37355
#: assets/js/packages/editor-app-bar/editor-app-bar.js:12
#: assets/js/packages/editor-app-bar/editor-app-bar.strings.js:19
msgid "Elements"
msgstr "المان‌ها"

#: core/admin/admin.php:343 core/admin/menu/main.php:75
#: core/common/modules/finder/categories/settings.php:29
#: core/dynamic-tags/base-tag.php:171 includes/editor-templates/panel.php:80
#: includes/managers/controls.php:339
#: includes/settings/admin-menu-items/admin-menu-item.php:28
#: includes/settings/settings.php:216
#: modules/atomic-widgets/elements/div-block/div-block.php:51
#: modules/usage/settings-reporter.php:13 assets/js/editor.js:9539
#: assets/js/editor.js:38245 assets/js/editor.js:47559
#: assets/js/packages/editor-editing-panel/editor-editing-panel.js:8
#: assets/js/packages/editor-editing-panel/editor-editing-panel.strings.js:35
msgid "Settings"
msgstr "تنظیمات"

#: includes/controls/groups/typography.php:179
msgctxt "Typography Control"
msgid "Italic"
msgstr "ایتالیک"

#: includes/controls/groups/typography.php:180
msgctxt "Typography Control"
msgid "Oblique"
msgstr "مورب"

#: includes/controls/groups/typography.php:165
msgctxt "Typography Control"
msgid "Uppercase"
msgstr "همه حروف بزرگ"

#: includes/controls/groups/typography.php:166
msgctxt "Typography Control"
msgid "Lowercase"
msgstr "همه حروف کوچک"

#: includes/controls/groups/typography.php:167
msgctxt "Typography Control"
msgid "Capitalize"
msgstr "حروف اول بزرگ"

#: includes/fonts.php:71
#: assets/js/packages/editor-controls/editor-controls.js:10
msgid "System"
msgstr "سیستم"

#: includes/base/element-base.php:1320 includes/controls/dimensions.php:83
#: includes/elements/column.php:737 includes/elements/container.php:1539
#: includes/elements/section.php:1205 includes/widgets/common-base.php:458
#: includes/widgets/common-base.php:459 includes/widgets/divider.php:482
#: includes/widgets/divider.php:778 includes/widgets/divider.php:944
#: includes/widgets/heading.php:263 includes/widgets/icon-box.php:251
#: includes/widgets/icon-box.php:305 includes/widgets/icon-list.php:272
#: includes/widgets/icon-list.php:554 includes/widgets/icon.php:197
#: includes/widgets/image-box.php:225 includes/widgets/image-box.php:280
#: includes/widgets/image-carousel.php:539
#: includes/widgets/image-carousel.php:845
#: includes/widgets/image-gallery.php:379 includes/widgets/image.php:271
#: includes/widgets/image.php:589 includes/widgets/social-icons.php:336
#: includes/widgets/star-rating.php:227 includes/widgets/tabs.php:434
#: includes/widgets/testimonial.php:234 includes/widgets/text-editor.php:270
#: includes/widgets/traits/button-trait.php:263
#: modules/floating-buttons/base/widget-contact-button-base.php:1138
#: modules/floating-buttons/base/widget-contact-button-base.php:2404
#: modules/floating-buttons/base/widget-contact-button-base.php:2946
#: modules/floating-buttons/base/widget-floating-bars-base.php:456
#: modules/floating-buttons/base/widget-floating-bars-base.php:1057
#: modules/shapes/widgets/text-path.php:185
#: assets/js/packages/editor-controls/editor-controls.js:10
#: assets/js/packages/editor-editing-panel/editor-editing-panel.js:8
#: assets/js/packages/editor-editing-panel/editor-editing-panel.strings.js:49
#: assets/js/packages/editor-editing-panel/editor-editing-panel.strings.js:82
#: assets/js/packages/editor-editing-panel/editor-editing-panel.strings.js:131
msgid "Right"
msgstr "راست"

#: includes/base/element-base.php:1312 includes/controls/dimensions.php:85
#: includes/elements/column.php:729 includes/elements/container.php:1538
#: includes/elements/section.php:1197 includes/widgets/common-base.php:458
#: includes/widgets/common-base.php:459 includes/widgets/divider.php:474
#: includes/widgets/divider.php:770 includes/widgets/divider.php:936
#: includes/widgets/heading.php:255 includes/widgets/icon-box.php:243
#: includes/widgets/icon-box.php:297 includes/widgets/icon-list.php:264
#: includes/widgets/icon-list.php:546 includes/widgets/icon.php:189
#: includes/widgets/image-box.php:217 includes/widgets/image-box.php:272
#: includes/widgets/image-carousel.php:538
#: includes/widgets/image-carousel.php:837
#: includes/widgets/image-gallery.php:371 includes/widgets/image.php:263
#: includes/widgets/image.php:581 includes/widgets/social-icons.php:328
#: includes/widgets/star-rating.php:219 includes/widgets/tabs.php:426
#: includes/widgets/testimonial.php:226 includes/widgets/text-editor.php:262
#: includes/widgets/traits/button-trait.php:255
#: modules/floating-buttons/base/widget-contact-button-base.php:1134
#: modules/floating-buttons/base/widget-contact-button-base.php:2400
#: modules/floating-buttons/base/widget-contact-button-base.php:2938
#: modules/floating-buttons/base/widget-floating-bars-base.php:452
#: modules/floating-buttons/base/widget-floating-bars-base.php:1053
#: modules/shapes/widgets/text-path.php:177
#: assets/js/packages/editor-controls/editor-controls.js:10
#: assets/js/packages/editor-editing-panel/editor-editing-panel.js:8
#: assets/js/packages/editor-editing-panel/editor-editing-panel.strings.js:47
#: assets/js/packages/editor-editing-panel/editor-editing-panel.strings.js:84
#: assets/js/packages/editor-editing-panel/editor-editing-panel.strings.js:133
msgid "Left"
msgstr "چپ"

#: core/kits/documents/tabs/theme-style-typography.php:109
#: includes/elements/container.php:595 includes/widgets/audio.php:111
#: includes/widgets/heading.php:177 includes/widgets/icon-box.php:195
#: includes/widgets/icon-list.php:169 includes/widgets/icon.php:164
#: includes/widgets/image-box.php:170 includes/widgets/image-carousel.php:337
#: includes/widgets/image-carousel.php:351
#: includes/widgets/image-gallery.php:186 includes/widgets/image.php:192
#: includes/widgets/image.php:209 includes/widgets/social-icons.php:194
#: includes/widgets/testimonial.php:183
#: includes/widgets/traits/button-trait.php:99 includes/widgets/video.php:150
#: includes/widgets/video.php:175 includes/widgets/video.php:199
#: modules/floating-buttons/base/widget-contact-button-base.php:418
#: modules/floating-buttons/base/widget-contact-button-base.php:931
#: modules/floating-buttons/base/widget-contact-button-base.php:1059
#: modules/floating-buttons/base/widget-floating-bars-base.php:164
#: modules/floating-buttons/base/widget-floating-bars-base.php:350
#: modules/floating-buttons/base/widget-floating-bars-base.php:568
#: modules/link-in-bio/base/widget-link-in-bio-base.php:263
#: modules/link-in-bio/base/widget-link-in-bio-base.php:405
#: modules/link-in-bio/base/widget-link-in-bio-base.php:640
#: modules/link-in-bio/base/widget-link-in-bio-base.php:1096
#: modules/shapes/widgets/text-path.php:158
#: assets/js/packages/editor-controls/editor-controls.js:10
msgid "Link"
msgstr "پیوند"

#: includes/fonts.php:76
msgid "Google"
msgstr "گوگل"

#: includes/controls/groups/background.php:428
msgctxt "Background Control"
msgid "Attachment"
msgstr "ضمیمه"

#: includes/controls/groups/background.php:433
msgctxt "Background Control"
msgid "Scroll"
msgstr "نوار پیمایش"

#: includes/controls/groups/background.php:434
msgctxt "Background Control"
msgid "Fixed"
msgstr "ثابت"

#: includes/controls/groups/background.php:457
msgctxt "Background Control"
msgid "Repeat"
msgstr "تکرار"

#: includes/controls/groups/typography.php:106
msgctxt "Typography Control"
msgid "Family"
msgstr "خانواده فونت"

#: includes/controls/icon.php:876 includes/controls/icon.php:877
msgid "Select Icon"
msgstr "انتخاب آیکن"

#: core/base/providers/social-network-provider.php:138
#: includes/widgets/video.php:137
msgid "YouTube"
msgstr "یوتیوب"

#: includes/widgets/toggle.php:134
msgid "Toggle Title"
msgstr "عنوان تغییر وضعیت"

#: includes/widgets/toggle.php:147
msgid "Toggle Content"
msgstr "محتوای تغییر وضعیت"

#: includes/widgets/progress.php:174
msgid "Percentage"
msgstr "درصد"

#: core/kits/documents/tabs/settings-background.php:79
#: core/kits/documents/tabs/settings-lightbox.php:99
#: core/kits/documents/tabs/settings-lightbox.php:116
#: includes/controls/animation.php:155 includes/controls/groups/border.php:64
#: includes/controls/groups/flex-item.php:130
#: includes/controls/groups/typography.php:193
#: includes/controls/hover-animation.php:129 includes/controls/icons.php:108
#: includes/controls/icons.php:194 includes/elements/container.php:1167
#: includes/elements/section.php:964 includes/widgets/divider.php:501
#: includes/widgets/image-carousel.php:218
#: includes/widgets/image-carousel.php:341
#: includes/widgets/image-carousel.php:393
#: includes/widgets/image-gallery.php:174
#: includes/widgets/image-gallery.php:192 includes/widgets/image.php:161
#: includes/widgets/image.php:196 includes/widgets/video.php:579
#: modules/nested-tabs/widgets/nested-tabs.php:406 assets/js/ai-admin.js:11233
#: assets/js/ai-admin.js:11239 assets/js/ai-admin.js:11251
#: assets/js/ai-admin.js:11262 assets/js/ai-admin.js:11273
#: assets/js/ai-admin.js:11284 assets/js/ai-admin.js:11300
#: assets/js/ai-gutenberg.js:13081 assets/js/ai-gutenberg.js:13087
#: assets/js/ai-gutenberg.js:13099 assets/js/ai-gutenberg.js:13110
#: assets/js/ai-gutenberg.js:13121 assets/js/ai-gutenberg.js:13132
#: assets/js/ai-gutenberg.js:13148 assets/js/ai-media-library.js:12862
#: assets/js/ai-media-library.js:12868 assets/js/ai-media-library.js:12880
#: assets/js/ai-media-library.js:12891 assets/js/ai-media-library.js:12902
#: assets/js/ai-media-library.js:12913 assets/js/ai-media-library.js:12929
#: assets/js/ai-unify-product-images.js:12862
#: assets/js/ai-unify-product-images.js:12868
#: assets/js/ai-unify-product-images.js:12880
#: assets/js/ai-unify-product-images.js:12891
#: assets/js/ai-unify-product-images.js:12902
#: assets/js/ai-unify-product-images.js:12913
#: assets/js/ai-unify-product-images.js:12929 assets/js/ai.js:14309
#: assets/js/ai.js:14315 assets/js/ai.js:14327 assets/js/ai.js:14338
#: assets/js/ai.js:14349 assets/js/ai.js:14360 assets/js/ai.js:14376
#: assets/js/packages/editor-editing-panel/editor-editing-panel.js:8
#: assets/js/packages/editor-editing-panel/editor-editing-panel.strings.js:135
msgid "None"
msgstr "هیچ"

#: core/kits/manager.php:139 includes/controls/groups/background.php:329
#: includes/controls/groups/background.php:487
#: includes/controls/groups/flex-item.php:24
#: includes/controls/groups/flex-item.php:93
#: includes/controls/groups/flex-item.php:142
#: includes/controls/groups/image-size.php:383
#: includes/elements/container.php:1428 includes/elements/container.php:1472
#: includes/elements/section.php:293 includes/maintenance-mode.php:238
#: includes/widgets/common-base.php:144 includes/widgets/common-base.php:234
#: includes/widgets/common-base.php:306 includes/widgets/common-base.php:350
#: includes/widgets/common-base.php:1046 includes/widgets/common-base.php:1103
#: includes/widgets/image-carousel.php:764
#: includes/widgets/image-gallery.php:290 includes/widgets/social-icons.php:213
#: includes/widgets/social-icons.php:366
#: modules/floating-buttons/base/widget-contact-button-base.php:1212
#: modules/floating-buttons/base/widget-contact-button-base.php:1264
#: modules/floating-buttons/base/widget-contact-button-base.php:1351
#: modules/floating-buttons/base/widget-contact-button-base.php:1560
#: modules/floating-buttons/base/widget-contact-button-base.php:1726
#: modules/floating-buttons/base/widget-contact-button-base.php:2290
#: modules/floating-buttons/base/widget-contact-button-base.php:2616
#: modules/floating-buttons/base/widget-contact-button-base.php:2685
#: modules/floating-buttons/base/widget-contact-button-base.php:2816
#: modules/shapes/module.php:52 assets/js/editor.js:45582
#: assets/js/packages/editor-editing-panel/editor-editing-panel.js:8
#: assets/js/packages/editor-editing-panel/editor-editing-panel.strings.js:100
#: assets/js/packages/editor-editing-panel/editor-editing-panel.strings.js:107
msgid "Custom"
msgstr "سفارشی"

#: includes/widgets/progress.php:201 includes/widgets/progress.php:274
msgid "Inner Text"
msgstr "متن داخلی"

#: includes/widgets/progress.php:207
msgid "Web Designer"
msgstr "طراح وب"

#: modules/floating-buttons/base/widget-contact-button-base.php:1844
msgid "Chat Background Color"
msgstr "رنگ پس‌زمینه گفتگو"

#: includes/widgets/sidebar.php:91
msgid "No sidebars were found"
msgstr "هیچ ستون کناری‌ای یافت نشد"

#: includes/widgets/video.php:623 includes/widgets/video.php:630
#: includes/widgets/video.php:785
msgid "Image Overlay"
msgstr "پوشش روی تصویر"

#: includes/widgets/video.php:670 includes/widgets/video.php:797
#: modules/floating-buttons/base/widget-floating-bars-base.php:267
msgid "Play Icon"
msgstr "آیکن پخش"

#: includes/widgets/toggle.php:46 includes/widgets/toggle.php:123
#: includes/widgets/toggle.php:276
msgid "Toggle"
msgstr "تغییر وضعیت"

#: includes/widgets/image-box.php:45 includes/widgets/image-box.php:110
msgid "Image Box"
msgstr "کادر تصویر"

#: includes/widgets/image-box.php:298 includes/widgets/image-carousel.php:776
msgid "Image Spacing"
msgstr "فاصله حواشی"

#: includes/widgets/image-box.php:212 includes/widgets/testimonial.php:196
msgid "Image Position"
msgstr "موقعیت تصویر"

#: includes/widgets/progress.php:45 includes/widgets/progress.php:110
#: includes/widgets/progress.php:217
msgid "Progress Bar"
msgstr "نوار پیشرفت"

#: includes/widgets/icon-list.php:45 includes/widgets/icon-list.php:110
msgid "Icon List"
msgstr "لیست آیکن"

#: modules/floating-buttons/base/widget-contact-button-base.php:205
#: modules/floating-buttons/base/widget-contact-button-base.php:235
#: modules/floating-buttons/base/widget-contact-button-base.php:1317
#: modules/floating-buttons/base/widget-contact-button-base.php:2017
#: modules/floating-buttons/base/widget-contact-button-base.php:2626
#: modules/floating-buttons/base/widget-contact-button-base.php:2695
#: modules/floating-buttons/base/widget-floating-bars-base.php:1141
msgid "Icon Color"
msgstr "رنگ آیکن"

#: includes/widgets/divider.php:898 includes/widgets/icon-box.php:399
#: includes/widgets/icon-box.php:440 includes/widgets/icon.php:220
#: includes/widgets/icon.php:263 includes/widgets/social-icons.php:221
#: includes/widgets/social-icons.php:374 includes/widgets/social-icons.php:531
#: includes/widgets/text-editor.php:436
msgid "Primary Color"
msgstr "رنگ اولیه"

#: includes/widgets/divider.php:915 includes/widgets/icon-box.php:415
#: includes/widgets/icon-box.php:453 includes/widgets/icon.php:237
#: includes/widgets/icon.php:277 includes/widgets/social-icons.php:235
#: includes/widgets/social-icons.php:388 includes/widgets/social-icons.php:546
#: includes/widgets/text-editor.php:451
msgid "Secondary Color"
msgstr "رنگ ثانویه"

#: includes/widgets/html.php:42
msgid "HTML"
msgstr "HTML"

#: includes/widgets/html.php:97 includes/widgets/html.php:104
msgid "HTML Code"
msgstr "کد HTML"

#: core/kits/documents/tabs/theme-style-images.php:21
#: core/kits/documents/tabs/theme-style-images.php:51
#: includes/controls/groups/background.php:623
#: includes/widgets/image-gallery.php:278 assets/js/ai-admin.js:3604
#: assets/js/ai-gutenberg.js:5372 assets/js/ai-media-library.js:5233
#: assets/js/ai-unify-product-images.js:5233 assets/js/ai.js:6045
msgid "Images"
msgstr "تصاویر"

#: core/base/traits/shared-widget-controls-trait.php:23
#: includes/elements/column.php:258 includes/elements/container.php:574
#: includes/elements/section.php:498 includes/widgets/divider.php:538
#: includes/widgets/heading.php:211
#: modules/atomic-widgets/elements/div-block/div-block.php:54
msgid "HTML Tag"
msgstr "تگ HTML"

#: includes/widgets/spacer.php:42 includes/widgets/spacer.php:123
msgid "Spacer"
msgstr "فاصله گذار"

#: core/kits/documents/tabs/theme-style-form-fields.php:78
#: core/kits/documents/tabs/theme-style-typography.php:126
#: core/kits/documents/tabs/theme-style-typography.php:155
#: core/kits/documents/tabs/theme-style-typography.php:200
#: includes/controls/box-shadow.php:104
#: includes/controls/groups/background.php:170
#: includes/controls/text-shadow.php:97 includes/elements/column.php:452
#: includes/elements/container.php:853 includes/elements/container.php:1217
#: includes/elements/section.php:726 includes/elements/section.php:1014
#: includes/widgets/accordion.php:337 includes/widgets/accordion.php:439
#: includes/widgets/accordion.php:508 includes/widgets/alert.php:418
#: includes/widgets/alert.php:435 includes/widgets/divider.php:589
#: includes/widgets/divider.php:733 includes/widgets/heading.php:320
#: includes/widgets/icon-box.php:613 includes/widgets/icon-box.php:664
#: includes/widgets/icon-list.php:396 includes/widgets/icon-list.php:433
#: includes/widgets/icon-list.php:458 includes/widgets/icon-list.php:662
#: includes/widgets/icon-list.php:686 includes/widgets/image-box.php:534
#: includes/widgets/image-box.php:585 includes/widgets/image-carousel.php:609
#: includes/widgets/image-carousel.php:693 includes/widgets/progress.php:225
#: includes/widgets/progress.php:283 includes/widgets/rating.php:110
#: includes/widgets/social-icons.php:208 includes/widgets/social-icons.php:361
#: includes/widgets/star-rating.php:378 includes/widgets/tabs.php:366
#: includes/widgets/tabs.php:459 includes/widgets/toggle.php:370
#: includes/widgets/toggle.php:463 includes/widgets/toggle.php:532
#: includes/widgets/video.php:809
#: modules/floating-buttons/base/widget-contact-button-base.php:1886
#: modules/floating-buttons/base/widget-contact-button-base.php:2285
#: modules/floating-buttons/base/widget-contact-button-base.php:2298
#: modules/floating-buttons/base/widget-contact-button-base.php:2536
#: modules/floating-buttons/base/widget-floating-bars-base.php:422
#: modules/floating-buttons/base/widget-floating-bars-base.php:540
#: modules/floating-buttons/base/widget-floating-bars-base.php:1074
#: modules/floating-buttons/base/widget-floating-bars-base.php:1275
#: modules/link-in-bio/base/widget-link-in-bio-base.php:1204
#: modules/link-in-bio/base/widget-link-in-bio-base.php:1389
#: modules/nested-accordion/widgets/nested-accordion.php:686
#: modules/nested-accordion/widgets/nested-accordion.php:749
#: modules/nested-tabs/widgets/nested-tabs.php:744
#: modules/nested-tabs/widgets/nested-tabs.php:780
#: modules/nested-tabs/widgets/nested-tabs.php:816
#: modules/nested-tabs/widgets/nested-tabs.php:942
#: modules/nested-tabs/widgets/nested-tabs.php:959
#: modules/nested-tabs/widgets/nested-tabs.php:976
#: modules/shapes/widgets/text-path.php:416
#: modules/shapes/widgets/text-path.php:440
#: modules/shapes/widgets/text-path.php:508
#: modules/shapes/widgets/text-path.php:528
#: modules/shapes/widgets/text-path.php:579
#: modules/shapes/widgets/text-path.php:599 assets/js/editor.js:47697
#: assets/js/editor.js:47740
#: assets/js/packages/editor-controls/editor-controls.js:8
#: assets/js/packages/editor-controls/editor-controls.js:10
msgid "Color"
msgstr "رنگ"

#: includes/controls/gallery.php:94 includes/widgets/image-carousel.php:141
#: includes/widgets/image-gallery.php:137
msgid "Add Images"
msgstr "افزودن تصاویر"

#: includes/widgets/image-carousel.php:342
#: includes/widgets/image-gallery.php:190 includes/widgets/image.php:197
msgid "Media File"
msgstr "فایل رسانه"

#: includes/widgets/image-gallery.php:267
msgid "Random"
msgstr "تصادفی"

#: core/dynamic-tags/tag.php:95 includes/widgets/counter.php:279
#: modules/nested-tabs/widgets/nested-tabs.php:207
#: modules/nested-tabs/widgets/nested-tabs.php:873
msgid "Before"
msgstr "قبل"

#: core/dynamic-tags/tag.php:105 includes/widgets/counter.php:283
#: modules/nested-tabs/widgets/nested-tabs.php:203
#: modules/nested-tabs/widgets/nested-tabs.php:865
msgid "After"
msgstr "بعد"

#: includes/controls/groups/background.php:292
#: includes/widgets/common-base.php:1004 includes/widgets/image-box.php:351
#: includes/widgets/image-carousel.php:724 includes/widgets/image.php:45
#: includes/widgets/image.php:126 includes/widgets/image.php:251
#: includes/widgets/testimonial.php:306
#: assets/js/packages/editor-controls/editor-controls.js:10
msgid "Image"
msgstr "تصویر"

#: includes/controls/media.php:189 includes/widgets/image-box.php:117
#: includes/widgets/image.php:133 includes/widgets/testimonial.php:131
#: includes/widgets/video.php:641
#: modules/link-in-bio/base/widget-link-in-bio-base.php:251
#: modules/link-in-bio/base/widget-link-in-bio-base.php:341
#: modules/link-in-bio/base/widget-link-in-bio-base.php:929
#: modules/link-in-bio/base/widget-link-in-bio-base.php:984
msgid "Choose Image"
msgstr "تصویر را انتخاب کنید"

#: includes/widgets/audio.php:152 includes/widgets/image-carousel.php:432
#: includes/widgets/video.php:339
msgid "Autoplay"
msgstr "پخش خودکار"

#: core/kits/documents/tabs/settings-lightbox.php:56
#: includes/widgets/counter.php:45 includes/widgets/counter.php:127
#: includes/widgets/counter.php:267
msgid "Counter"
msgstr "شمارنده"

#: includes/widgets/counter.php:134
msgid "Starting Number"
msgstr "عدد آغازین"

#: includes/widgets/counter.php:146
msgid "Ending Number"
msgstr "عدد پایانی"

#: includes/widgets/counter.php:158
msgid "Number Prefix"
msgstr "پیشوند عدد"

#: includes/widgets/button.php:48 includes/widgets/button.php:93
#: includes/widgets/button.php:114
#: modules/floating-buttons/base/widget-floating-bars-base.php:567
#: modules/link-in-bio/base/widget-link-in-bio-base.php:1095
msgid "Button"
msgstr "دکمه"

#: includes/widgets/heading.php:197 includes/widgets/traits/button-trait.php:37
#: modules/floating-buttons/base/widget-contact-button-base.php:1120
#: modules/floating-buttons/base/widget-contact-button-base.php:1545
#: modules/floating-buttons/base/widget-contact-button-base.php:1927
#: modules/floating-buttons/base/widget-contact-button-base.php:2277
#: modules/link-in-bio/base/widget-link-in-bio-base.php:1406
msgid "Large"
msgstr "بزرگ"

#: includes/widgets/heading.php:198
msgid "XL"
msgstr "ایکس لارج"

#: includes/widgets/heading.php:199
msgid "XXL"
msgstr "دو ایکس لارج"

#: includes/widgets/accordion.php:185 includes/widgets/accordion.php:408
#: includes/widgets/alert.php:173 includes/widgets/divider.php:509
#: includes/widgets/divider.php:561 includes/widgets/divider.php:821
#: includes/widgets/icon-box.php:117 includes/widgets/icon-box.php:379
#: includes/widgets/icon-list.php:156 includes/widgets/icon-list.php:416
#: includes/widgets/icon.php:44 includes/widgets/icon.php:111
#: includes/widgets/icon.php:118 includes/widgets/icon.php:177
#: includes/widgets/rating.php:52 includes/widgets/rating.php:177
#: includes/widgets/social-icons.php:116 includes/widgets/social-icons.php:353
#: includes/widgets/star-rating.php:168 includes/widgets/toggle.php:188
#: includes/widgets/toggle.php:432 includes/widgets/traits/button-trait.php:126
#: includes/widgets/video.php:686
#: modules/floating-buttons/base/widget-contact-button-base.php:495
#: modules/floating-buttons/base/widget-floating-bars-base.php:113
#: modules/floating-buttons/base/widget-floating-bars-base.php:181
#: modules/floating-buttons/base/widget-floating-bars-base.php:325
#: modules/floating-buttons/base/widget-floating-bars-base.php:399
#: modules/floating-buttons/base/widget-floating-bars-base.php:1267
#: modules/nested-accordion/widgets/nested-accordion.php:205
#: modules/nested-accordion/widgets/nested-accordion.php:595
#: modules/nested-tabs/widgets/nested-tabs.php:126
#: modules/nested-tabs/widgets/nested-tabs.php:847
msgid "Icon"
msgstr "آیکن"

#: includes/widgets/alert.php:124 includes/widgets/progress.php:160
#: includes/widgets/traits/button-trait.php:74
msgid "Warning"
msgstr "هشدار"

#: includes/widgets/alert.php:125 includes/widgets/progress.php:161
#: includes/widgets/traits/button-trait.php:75
msgid "Danger"
msgstr "خطر"

#: core/kits/documents/tabs/settings-lightbox.php:130
#: core/kits/documents/tabs/theme-style-form-fields.php:200
#: includes/controls/groups/background.php:174 includes/widgets/alert.php:218
#: includes/widgets/image.php:622 includes/widgets/progress.php:239
#: includes/widgets/tabs.php:345 includes/widgets/video.php:882
#: modules/floating-buttons/base/widget-contact-button-base.php:1235
#: modules/floating-buttons/base/widget-contact-button-base.php:1287
#: modules/floating-buttons/base/widget-contact-button-base.php:1328
#: modules/floating-buttons/base/widget-contact-button-base.php:1374
#: modules/floating-buttons/base/widget-contact-button-base.php:1690
#: modules/floating-buttons/base/widget-contact-button-base.php:1960
#: modules/floating-buttons/base/widget-contact-button-base.php:1996
#: modules/floating-buttons/base/widget-contact-button-base.php:2029
#: modules/floating-buttons/base/widget-contact-button-base.php:2133
#: modules/floating-buttons/base/widget-contact-button-base.php:2159
#: modules/floating-buttons/base/widget-contact-button-base.php:2368
#: modules/floating-buttons/base/widget-contact-button-base.php:2655
#: modules/floating-buttons/base/widget-contact-button-base.php:2724
#: modules/floating-buttons/base/widget-contact-button-base.php:2811
#: modules/floating-buttons/base/widget-contact-button-base.php:2825
#: modules/floating-buttons/base/widget-floating-bars-base.php:674
#: modules/floating-buttons/base/widget-floating-bars-base.php:708
#: modules/floating-buttons/base/widget-floating-bars-base.php:1110
#: modules/floating-buttons/base/widget-floating-bars-base.php:1155
#: modules/link-in-bio/base/widget-link-in-bio-base.php:1125
#: modules/nested-tabs/widgets/nested-tabs.php:506
#: modules/nested-tabs/widgets/nested-tabs.php:564
#: modules/nested-tabs/widgets/nested-tabs.php:648
#: modules/nested-tabs/widgets/nested-tabs.php:1003 assets/js/ai-admin.js:14001
#: assets/js/ai-admin.js:14723 assets/js/ai-gutenberg.js:15849
#: assets/js/ai-gutenberg.js:16571 assets/js/ai-media-library.js:15630
#: assets/js/ai-media-library.js:16352
#: assets/js/ai-unify-product-images.js:15630
#: assets/js/ai-unify-product-images.js:16352 assets/js/ai.js:17077
#: assets/js/ai.js:17799
msgid "Background Color"
msgstr "رنگ پس زمینه"

#: includes/widgets/image-carousel.php:478
msgid "Autoplay Speed"
msgstr "سرعت پخش خودکار"

#: includes/widgets/accordion.php:352 includes/widgets/accordion.php:451
#: includes/widgets/image-carousel.php:708 includes/widgets/tabs.php:380
#: includes/widgets/toggle.php:385 includes/widgets/toggle.php:475
msgid "Active Color"
msgstr "رنگ فعال"

#: core/base/document.php:1970
#: core/kits/documents/tabs/settings-lightbox.php:96
#: core/kits/documents/tabs/settings-lightbox.php:100
#: core/kits/documents/tabs/settings-lightbox.php:117
#: includes/elements/column.php:126 includes/elements/section.php:240
#: includes/widgets/accordion.php:132 includes/widgets/accordion.php:318
#: includes/widgets/alert.php:134 includes/widgets/alert.php:262
#: includes/widgets/common-base.php:191 includes/widgets/counter.php:228
#: includes/widgets/counter.php:542 includes/widgets/heading.php:161
#: includes/widgets/icon-box.php:167 includes/widgets/icon-box.php:604
#: includes/widgets/image-box.php:142 includes/widgets/image-box.php:525
#: includes/widgets/image-carousel.php:394 includes/widgets/progress.php:117
#: includes/widgets/star-rating.php:203 includes/widgets/star-rating.php:247
#: includes/widgets/tabs.php:127 includes/widgets/tabs.php:357
#: includes/widgets/testimonial.php:168 includes/widgets/testimonial.php:404
#: includes/widgets/toggle.php:132 includes/widgets/toggle.php:350
#: modules/atomic-widgets/elements/atomic-heading/atomic-heading.php:60
#: modules/floating-buttons/base/widget-contact-button-base.php:129
#: modules/floating-buttons/base/widget-contact-button-base.php:210
#: modules/floating-buttons/base/widget-contact-button-base.php:2312
#: modules/link-in-bio/base/widget-link-in-bio-base.php:866
#: modules/nested-accordion/widgets/nested-accordion.php:115
#: modules/nested-accordion/widgets/nested-accordion.php:563
#: modules/nested-tabs/widgets/nested-tabs.php:113
msgid "Title"
msgstr "عنوان"

#: includes/editor-templates/templates.php:140
#: includes/widgets/testimonial.php:153 includes/widgets/testimonial.php:359
#: modules/floating-buttons/base/widget-contact-button-base.php:121
#: modules/floating-buttons/base/widget-contact-button-base.php:209
#: modules/floating-buttons/base/widget-contact-button-base.php:653
#: modules/floating-buttons/base/widget-contact-button-base.php:1735
msgid "Name"
msgstr "نام"

#: core/base/traits/shared-widget-controls-trait.php:159
#: core/settings/editor-preferences/model.php:140
#: core/settings/editor-preferences/model.php:152
#: core/settings/editor-preferences/model.php:163
#: core/settings/editor-preferences/model.php:208
#: includes/controls/switcher.php:73 includes/managers/icons.php:469
#: includes/widgets/audio.php:135 includes/widgets/image-carousel.php:202
#: includes/widgets/image-carousel.php:378
#: includes/widgets/image-carousel.php:435
#: includes/widgets/image-carousel.php:448
#: includes/widgets/image-carousel.php:465
#: includes/widgets/image-carousel.php:496
#: includes/widgets/image-gallery.php:212 includes/widgets/image.php:237
#: modules/floating-buttons/base/widget-contact-button-base.php:3046
#: modules/floating-buttons/base/widget-floating-bars-base.php:764
#: modules/floating-buttons/base/widget-floating-bars-base.php:1456
#: modules/link-in-bio/base/widget-link-in-bio-base.php:1554
#: modules/link-in-bio/base/widget-link-in-bio-base.php:1612
#: modules/nested-accordion/widgets/nested-accordion.php:310
#: modules/styleguide/module.php:127
msgid "No"
msgstr "خیر"

#: core/base/traits/shared-widget-controls-trait.php:158
#: core/settings/editor-preferences/model.php:139
#: core/settings/editor-preferences/model.php:151
#: core/settings/editor-preferences/model.php:162
#: core/settings/editor-preferences/model.php:207
#: includes/controls/switcher.php:74 includes/managers/icons.php:470
#: includes/widgets/audio.php:134 includes/widgets/image-carousel.php:203
#: includes/widgets/image-carousel.php:377
#: includes/widgets/image-carousel.php:434
#: includes/widgets/image-carousel.php:447
#: includes/widgets/image-carousel.php:464
#: includes/widgets/image-carousel.php:495
#: includes/widgets/image-gallery.php:211 includes/widgets/image.php:236
#: modules/floating-buttons/base/widget-contact-button-base.php:3045
#: modules/floating-buttons/base/widget-floating-bars-base.php:763
#: modules/floating-buttons/base/widget-floating-bars-base.php:1455
#: modules/link-in-bio/base/widget-link-in-bio-base.php:1553
#: modules/link-in-bio/base/widget-link-in-bio-base.php:1611
#: modules/nested-accordion/widgets/nested-accordion.php:309
#: modules/styleguide/module.php:128 assets/js/app.js:8951
msgid "Yes"
msgstr "بله"

#: core/base/document.php:1962
#: core/common/modules/finder/categories/settings.php:49
msgid "General Settings"
msgstr "تنظیمات عمومی"

#: includes/elements/container.php:470 includes/elements/section.php:327
#: includes/elements/section.php:367
msgid "Min Height"
msgstr "کمترین ارتفاع"

#: includes/widgets/video.php:962
msgid "Content Position"
msgstr "موقعیت محتوا"

#: includes/elements/section.php:337 includes/elements/section.php:377
msgid "Minimum Height"
msgstr "کمترین ارتفاع"

#: includes/controls/image-dimensions.php:100
#: includes/elements/container.php:1265 includes/elements/section.php:321
#: includes/elements/section.php:361 includes/elements/section.php:1062
#: includes/widgets/google-maps.php:192 includes/widgets/icon-list.php:363
#: includes/widgets/image.php:354 includes/widgets/progress.php:250
#: assets/js/packages/editor-editing-panel/editor-editing-panel.js:8
#: assets/js/packages/editor-editing-panel/editor-editing-panel.strings.js:65
msgid "Height"
msgstr "ارتفاع"

#: includes/elements/column.php:160
msgid "Column Width"
msgstr "عرض ستون"

#: core/kits/documents/tabs/settings-layout.php:55
#: includes/elements/container.php:377 includes/elements/section.php:249
#: includes/widgets/video.php:942
#: modules/link-in-bio/base/widget-link-in-bio-base.php:1587
msgid "Content Width"
msgstr "عرض محتوا"

#: includes/controls/icons.php:80 includes/controls/media.php:238
msgid "Add"
msgstr "اضافه کردن"

#: includes/editor-templates/hotkeys.php:135
msgid "Page Settings"
msgstr "تنظیمات صفحه"

#: includes/managers/controls.php:335 includes/widgets/divider.php:385
#: includes/widgets/icon-list.php:297 assets/js/ai-admin.js:10510
#: assets/js/ai-gutenberg.js:12358 assets/js/ai-media-library.js:12139
#: assets/js/ai-unify-product-images.js:12139 assets/js/ai.js:13586
#: assets/js/editor.js:9542 assets/js/editor.js:37160
#: assets/js/packages/editor-editing-panel/editor-editing-panel.js:8
#: assets/js/packages/editor-editing-panel/editor-editing-panel.strings.js:13
#: assets/js/packages/editor-editing-panel/editor-editing-panel.strings.js:41
msgid "Style"
msgstr "استایل"

#: core/settings/editor-preferences/model.php:91
#: includes/controls/groups/background.php:499
#: includes/controls/image-dimensions.php:95
#: includes/elements/container.php:391 includes/elements/container.php:1231
#: includes/elements/section.php:263 includes/elements/section.php:1028
#: includes/widgets/common-base.php:227 includes/widgets/divider.php:443
#: includes/widgets/icon-list.php:344 includes/widgets/image-box.php:362
#: includes/widgets/image.php:284
#: modules/floating-buttons/base/widget-contact-button-base.php:2840
#: modules/nested-tabs/widgets/nested-tabs.php:312
#: modules/shapes/widgets/text-path.php:540
#: modules/shapes/widgets/text-path.php:611
#: assets/js/packages/editor-editing-panel/editor-editing-panel.js:8
#: assets/js/packages/editor-editing-panel/editor-editing-panel.strings.js:64
msgid "Width"
msgstr "عرض"

#: core/kits/documents/tabs/settings-lightbox.php:163
#: core/kits/documents/tabs/theme-style-buttons.php:98
#: core/kits/documents/tabs/theme-style-buttons.php:173
#: core/kits/documents/tabs/theme-style-form-fields.php:176
#: core/kits/documents/tabs/theme-style-typography.php:55
#: includes/elements/column.php:689 includes/elements/section.php:1157
#: includes/widgets/alert.php:270 includes/widgets/alert.php:310
#: includes/widgets/counter.php:499 includes/widgets/counter.php:553
#: includes/widgets/heading.php:351 includes/widgets/image-carousel.php:863
#: includes/widgets/image-gallery.php:400 includes/widgets/image.php:607
#: includes/widgets/progress.php:323 includes/widgets/star-rating.php:258
#: includes/widgets/testimonial.php:269 includes/widgets/testimonial.php:367
#: includes/widgets/testimonial.php:412 includes/widgets/text-editor.php:343
#: includes/widgets/traits/button-trait.php:347
#: includes/widgets/traits/button-trait.php:400
#: modules/floating-buttons/base/widget-contact-button-base.php:1581
#: modules/floating-buttons/base/widget-contact-button-base.php:1613
#: modules/floating-buttons/base/widget-contact-button-base.php:1744
#: modules/floating-buttons/base/widget-contact-button-base.php:1775
#: modules/floating-buttons/base/widget-contact-button-base.php:1806
#: modules/floating-buttons/base/widget-contact-button-base.php:2114
#: modules/floating-buttons/base/widget-contact-button-base.php:2321
#: modules/floating-buttons/base/widget-contact-button-base.php:2349
#: modules/floating-buttons/base/widget-contact-button-base.php:2642
#: modules/floating-buttons/base/widget-contact-button-base.php:2711
#: modules/floating-buttons/base/widget-floating-bars-base.php:663
#: modules/floating-buttons/base/widget-floating-bars-base.php:697
#: modules/floating-buttons/base/widget-floating-bars-base.php:1381
#: modules/floating-buttons/base/widget-floating-bars-base.php:1401
#: modules/link-in-bio/base/widget-link-in-bio-base.php:1105
#: modules/link-in-bio/base/widget-link-in-bio-base.php:1301
#: modules/link-in-bio/base/widget-link-in-bio-base.php:1329
#: modules/link-in-bio/base/widget-link-in-bio-base.php:1357
#: assets/js/packages/editor-editing-panel/editor-editing-panel.js:8
#: assets/js/packages/editor-editing-panel/editor-editing-panel.strings.js:46
msgid "Text Color"
msgstr "رنگ متن"

#: includes/managers/controls.php:334 includes/widgets/accordion.php:145
#: includes/widgets/accordion.php:489 includes/widgets/alert.php:148
#: includes/widgets/icon-box.php:596 includes/widgets/image-box.php:517
#: includes/widgets/tabs.php:141 includes/widgets/tabs.php:450
#: includes/widgets/testimonial.php:118 includes/widgets/testimonial.php:261
#: includes/widgets/toggle.php:145 includes/widgets/toggle.php:513
#: modules/atomic-widgets/elements/atomic-button/atomic-button.php:55
#: modules/atomic-widgets/elements/atomic-heading/atomic-heading.php:57
#: modules/atomic-widgets/elements/atomic-image/atomic-image.php:48
#: modules/atomic-widgets/elements/atomic-paragraph/atomic-paragraph.php:53
#: modules/atomic-widgets/elements/atomic-svg/atomic-svg.php:49
#: modules/nested-accordion/widgets/nested-accordion.php:492
#: modules/nested-tabs/widgets/nested-tabs.php:990 assets/js/app.js:10350
#: assets/js/app.js:10842 assets/js/editor.js:37157
msgid "Content"
msgstr "محتوا"

#: includes/controls/gaps.php:57
#: includes/controls/groups/grid-container.php:119
#: includes/elements/column.php:60
#: assets/js/packages/editor-controls/editor-controls.js:10
#: assets/js/packages/editor-editing-panel/editor-editing-panel.js:8
#: assets/js/packages/editor-editing-panel/editor-editing-panel.strings.js:111
msgid "Column"
msgstr "ستون"

#: modules/floating-buttons/documents/floating-buttons.php:31
msgid "Go To Dashboard"
msgstr "رفتن به پیشخوان"

#: core/breakpoints/manager.php:334
msgid "Laptop"
msgstr "لپ تاپ"

#: core/settings/editor-preferences/model.php:125
#: assets/js/kit-library.b0f0ab89c95fe1f6fde3.bundle.js:4345
msgid "Tablet"
msgstr "تبلت"

#: core/admin/admin.php:621 core/admin/menu/main.php:41
#: assets/js/packages/editor-app-bar/editor-app-bar.js:12
#: assets/js/packages/editor-app-bar/editor-app-bar.strings.js:2
msgid "Help"
msgstr "راهنما"

#: includes/editor-templates/hotkeys.php:81
#: includes/editor-templates/templates.php:22
#: includes/editor-templates/templates.php:23
#: includes/editor-templates/templates.php:294
#: includes/editor-templates/templates.php:306
#: includes/editor-templates/templates.php:319 assets/js/e-home-screen.js:246
#: assets/js/editor.js:45031 assets/js/element-manager-admin.js:2412
#: assets/js/kit-elements-defaults-editor.js:598
msgid "Save"
msgstr "ذخیره"

#: includes/widgets/alert.php:46 includes/widgets/alert.php:111
#: includes/widgets/alert.php:210
msgid "Alert"
msgstr "هشدار"

#: includes/controls/structure.php:65
msgid "Reset"
msgstr "تنظیم مجدد"

#: includes/editor-templates/hotkeys.php:172
#: includes/editor-templates/panel.php:99
msgid "Responsive Mode"
msgstr "حالت واکنشگرا"

#: core/kits/documents/tabs/global-colors.php:87
#: core/kits/documents/tabs/global-typography.php:142
#: includes/widgets/divider.php:505 includes/widgets/divider.php:523
#: includes/widgets/divider.php:722 includes/widgets/icon-list.php:142
#: includes/widgets/icon-list.php:626
#: includes/widgets/traits/button-trait.php:58
#: modules/floating-buttons/base/widget-contact-button-base.php:145
#: modules/floating-buttons/base/widget-contact-button-base.php:1042
#: modules/floating-buttons/base/widget-floating-bars-base.php:63
#: modules/floating-buttons/base/widget-floating-bars-base.php:151
#: modules/floating-buttons/base/widget-floating-bars-base.php:337
#: modules/floating-buttons/base/widget-floating-bars-base.php:1353
#: modules/link-in-bio/base/widget-link-in-bio-base.php:326
#: modules/link-in-bio/base/widget-link-in-bio-base.php:582
#: modules/shapes/widgets/text-path.php:111
#: modules/shapes/widgets/text-path.php:304 assets/js/ai-admin.js:3603
#: assets/js/ai-gutenberg.js:5371 assets/js/ai-media-library.js:5232
#: assets/js/ai-unify-product-images.js:5232 assets/js/ai.js:6044
msgid "Text"
msgstr "متن"

#: core/admin/admin.php:223 core/admin/admin.php:231 core/base/document.php:649
#: modules/admin-bar/module.php:124 modules/gutenberg/module.php:101
#: modules/gutenberg/module.php:112 modules/gutenberg/module.php:134
#: assets/js/e-wc-product-editor.js:2677
msgid "Edit with Elementor"
msgstr "ویرایش با المنتور"

#: includes/controls/groups/border.php:67 includes/widgets/divider.php:344
#: includes/widgets/icon-list.php:302
#: assets/js/packages/editor-editing-panel/editor-editing-panel.js:8
#: assets/js/packages/editor-editing-panel/editor-editing-panel.strings.js:138
msgid "Dotted"
msgstr "نقطه ای"

#: core/admin/admin.php:242
#: core/editor/loader/v1/templates/editor-body-v1-view.php:23
#: core/editor/loader/v2/templates/editor-body-v2-view.php:23
#: includes/editor-templates/templates.php:54 modules/gutenberg/module.php:123
#: assets/js/ai-admin.js:1741 assets/js/ai-gutenberg.js:3509
#: assets/js/ai-media-library.js:3370 assets/js/ai-unify-product-images.js:3370
#: assets/js/ai.js:4149 assets/js/app-packages.js:5283
#: assets/js/kit-library.b0f0ab89c95fe1f6fde3.bundle.js:236
#: assets/js/onboarding.120a44527e5a7209a8e4.bundle.js:46
msgid "Loading"
msgstr "در حال بارگذاری"

#: includes/editor-templates/global.php:34
msgid "Add New Section"
msgstr "افزودن بخش جدید"

#: core/base/traits/shared-widget-controls-trait.php:269
#: core/settings/editor-preferences/model.php:126
#: includes/base/element-base.php:1376 includes/editor-templates/panel.php:283
#: assets/js/kit-library.b0f0ab89c95fe1f6fde3.bundle.js:4338
#: assets/js/packages/editor-responsive/editor-responsive.js:2
#: assets/js/packages/editor-responsive/editor-responsive.strings.js:1
msgid "Desktop"
msgstr "دسکتاپ"

#: modules/history/views/revisions-panel-template.php:11
#: assets/js/editor.js:45032
msgid "Discard"
msgstr "بیخیال"

#: core/kits/documents/tabs/global-colors.php:77
#: core/kits/documents/tabs/global-typography.php:128
msgid "Primary"
msgstr "اولیه"

#: core/common/modules/finder/categories/general.php:55
#: modules/system-info/module.php:157
#: modules/system-info/system-info-menu-item.php:29
#: modules/system-info/system-info-menu-item.php:33
msgid "System Info"
msgstr "اطلاعات سیستم"

#: modules/system-info/module.php:164
msgid "Copy & Paste Info"
msgstr "اطلاعات را کپی و پیست کنید"

#: modules/system-info/module.php:160 modules/system-info/module.php:184
msgid "Download System Info"
msgstr "دانلود اطلاعات سیستم"

#: includes/widgets/counter.php:173
msgid "Number Suffix"
msgstr "پسوند عدد"

#: core/admin/admin-notices.php:240
msgid "Sure! I'd love to help"
msgstr "حتما ! کمک خواهم کرد"

#: core/common/modules/finder/categories/settings.php:59
#: core/dynamic-tags/tag.php:88 includes/elements/column.php:757
#: includes/elements/section.php:1225 includes/managers/controls.php:336
#: includes/settings/settings.php:329 includes/settings/settings.php:332
#: modules/floating-buttons/base/widget-contact-button-base.php:2919
#: modules/floating-buttons/base/widget-floating-bars-base.php:1419
#: modules/floating-buttons/module.php:78
#: modules/floating-buttons/module.php:83 assets/js/editor.js:9545
#: assets/js/editor.js:37163
#: assets/js/kit-library.b0f0ab89c95fe1f6fde3.bundle.js:3180
#: assets/js/kit-library.b0f0ab89c95fe1f6fde3.bundle.js:3184
#: assets/js/onboarding.120a44527e5a7209a8e4.bundle.js:1441
msgid "Advanced"
msgstr "پیشرفته"

#: includes/widgets/icon-box.php:237
#: includes/widgets/traits/button-trait.php:142
#: modules/floating-buttons/base/widget-contact-button-base.php:1130
#: modules/floating-buttons/base/widget-contact-button-base.php:2396
#: modules/floating-buttons/base/widget-floating-bars-base.php:576
#: modules/floating-buttons/base/widget-floating-bars-base.php:1286
msgid "Icon Position"
msgstr "محل قرارگیری آیکن"

#: includes/widgets/alert.php:122 includes/widgets/progress.php:158
#: includes/widgets/traits/button-trait.php:72 assets/js/app-packages.js:5651
#: assets/js/kit-library.b0f0ab89c95fe1f6fde3.bundle.js:3437
msgid "Info"
msgstr "اطلاعات"

#: includes/elements/section.php:401
msgid "Column Position"
msgstr "موقعیت ستون"

#: includes/elements/section.php:289
msgid "Narrow"
msgstr "باریک"

#: includes/elements/section.php:291
msgid "Wide"
msgstr "عریض"

#: includes/elements/section.php:326 includes/elements/section.php:366
msgid "Fit To Screen"
msgstr "هم اندازه صفحه نمایش"

#: includes/editor-templates/hotkeys.php:125
#: includes/editor-templates/navigator.php:38
#: includes/editor-templates/panel.php:86
#: includes/editor-templates/panel.php:90 includes/elements/section.php:511
#: includes/elements/section.php:519 assets/js/editor.js:31846
#: assets/js/packages/editor-app-bar/editor-app-bar.js:12
#: assets/js/packages/editor-app-bar/editor-app-bar.strings.js:7
msgid "Structure"
msgstr "ساختار"

#: includes/controls/groups/typography.php:160
msgctxt "Typography Control"
msgid "Transform"
msgstr "تغییر شکل"

#: includes/controls/groups/typography.php:173
msgctxt "Typography Control"
msgid "Style"
msgstr "استایل"

#: core/kits/documents/tabs/global-colors.php:92
#: core/kits/documents/tabs/global-typography.php:149
msgid "Accent"
msgstr "شدت"

#: includes/widgets/divider.php:837 includes/widgets/icon-box.php:135
#: includes/widgets/icon.php:136 includes/widgets/text-editor.php:426
msgid "Framed"
msgstr "قاب دار"

#: includes/widgets/accordion.php:147
msgid "Accordion Content"
msgstr "محتوای آکارئون"

#: includes/widgets/image-carousel.php:164
msgid "Slides to Show"
msgstr "اسلایدها جهت نمایش"

#: includes/widgets/image-carousel.php:181
msgid "Slides to Scroll"
msgstr "اسلایدها جهت پیمایش"

#: includes/widgets/common-base.php:990 includes/widgets/icon-box.php:148
#: includes/widgets/icon.php:146 includes/widgets/social-icons.php:280
msgid "Shape"
msgstr "شکل"

#: includes/widgets/counter.php:235
msgid "Cool Number"
msgstr "شماره خفن"

#: includes/widgets/icon-box.php:323
#: includes/widgets/traits/button-trait.php:175
#: modules/floating-buttons/base/widget-contact-button-base.php:1160
#: modules/floating-buttons/base/widget-contact-button-base.php:2416
#: modules/floating-buttons/base/widget-floating-bars-base.php:606
#: modules/floating-buttons/base/widget-floating-bars-base.php:1331
msgid "Icon Spacing"
msgstr "فاصله بین آیکن ها"

#: includes/widgets/divider.php:836 includes/widgets/icon-box.php:134
#: includes/widgets/icon.php:135 includes/widgets/text-editor.php:425
msgid "Stacked"
msgstr "انباشته"

#: includes/widgets/icon-list.php:145 includes/widgets/icon-list.php:146
msgid "List Item"
msgstr "آیتم لیست"

#: includes/elements/container.php:1875 includes/elements/section.php:1396
msgid "Visibility"
msgstr "نمایانی"

#. Plugin Name of the plugin
#: elementor.php app/view.php:23 core/admin/admin.php:292
#: core/admin/admin.php:409 core/admin/admin.php:487
#: core/admin/menu/main.php:17 core/admin/menu/main.php:18
#: core/documents-manager.php:384 core/upgrade/custom-tasks-manager.php:29
#: core/upgrade/manager.php:47 includes/editor-templates/navigator.php:101
#: includes/editor-templates/panel-elements.php:94
#: includes/editor-templates/panel.php:208
#: includes/editor-templates/templates.php:164 includes/plugin.php:867
#: includes/settings/admin-menu-items/admin-menu-item.php:29
#: includes/settings/settings.php:91 includes/settings/settings.php:92
#: includes/settings/settings.php:487 modules/compatibility-tag/module.php:36
#: modules/history/views/history-panel-template.php:23
#: modules/history/views/revisions-panel-template.php:38
#: assets/js/app-packages.js:1821 assets/js/app.js:2010
msgid "Elementor"
msgstr "المنتور"

#: includes/widgets/video.php:387
msgid "Player Controls"
msgstr "کنترل پخش کننده"

#: includes/base/element-base.php:1316 includes/base/element-base.php:1344
#: includes/controls/groups/flex-container.php:101
#: includes/controls/groups/flex-container.php:137
#: includes/controls/groups/flex-item.php:59
#: includes/controls/groups/grid-container.php:139
#: includes/controls/groups/grid-container.php:167
#: includes/elements/column.php:216 includes/elements/column.php:733
#: includes/elements/section.php:1201 includes/widgets/common-base.php:412
#: includes/widgets/counter.php:322 includes/widgets/counter.php:396
#: includes/widgets/counter.php:432 includes/widgets/divider.php:478
#: includes/widgets/divider.php:774 includes/widgets/divider.php:940
#: includes/widgets/heading.php:259 includes/widgets/icon-box.php:301
#: includes/widgets/icon-list.php:268 includes/widgets/icon-list.php:550
#: includes/widgets/icon-list.php:581 includes/widgets/icon.php:193
#: includes/widgets/image-box.php:276 includes/widgets/image-carousel.php:740
#: includes/widgets/image-carousel.php:841
#: includes/widgets/image-gallery.php:375 includes/widgets/image.php:267
#: includes/widgets/image.php:585 includes/widgets/rating.php:207
#: includes/widgets/social-icons.php:332 includes/widgets/star-rating.php:223
#: includes/widgets/tabs.php:213 includes/widgets/tabs.php:243
#: includes/widgets/tabs.php:430 includes/widgets/testimonial.php:230
#: includes/widgets/text-editor.php:266
#: includes/widgets/traits/button-trait.php:259
#: includes/widgets/traits/button-trait.php:291 includes/widgets/video.php:966
#: modules/floating-buttons/base/widget-contact-button-base.php:2942
#: modules/floating-buttons/base/widget-floating-bars-base.php:1193
#: modules/nested-accordion/widgets/nested-accordion.php:177
#: modules/nested-tabs/widgets/nested-tabs.php:238
#: modules/nested-tabs/widgets/nested-tabs.php:280
#: modules/nested-tabs/widgets/nested-tabs.php:350
#: modules/shapes/widgets/text-path.php:181
#: assets/js/packages/editor-editing-panel/editor-editing-panel.js:8
#: assets/js/packages/editor-editing-panel/editor-editing-panel.strings.js:48
#: assets/js/packages/editor-editing-panel/editor-editing-panel.strings.js:91
#: assets/js/packages/editor-editing-panel/editor-editing-panel.strings.js:121
#: assets/js/packages/editor-editing-panel/editor-editing-panel.strings.js:126
msgid "Center"
msgstr "وسط"

#: core/role-manager/role-manager.php:74
msgid "Exclude Roles"
msgstr "نادیده گرفتن نقش‌ها"

#: core/experiments/manager.php:395
#: core/kits/documents/tabs/settings-background.php:78
#: core/settings/editor-preferences/model.php:123
#: includes/base/widget-base.php:297 includes/controls/animation.php:154
#: includes/controls/font.php:66 includes/controls/groups/background.php:319
#: includes/controls/groups/background.php:432
#: includes/controls/groups/background.php:462
#: includes/controls/groups/background.php:483
#: includes/controls/groups/background.php:688
#: includes/controls/groups/background.php:707
#: includes/controls/groups/border.php:63
#: includes/controls/groups/flex-item.php:23
#: includes/controls/groups/typography.php:151
#: includes/controls/groups/typography.php:164
#: includes/controls/groups/typography.php:177
#: includes/controls/groups/typography.php:189
#: includes/editor-templates/panel.php:254 includes/elements/column.php:186
#: includes/elements/column.php:214 includes/elements/column.php:252
#: includes/elements/container.php:545 includes/elements/container.php:568
#: includes/elements/container.php:1526 includes/elements/section.php:287
#: includes/elements/section.php:325 includes/elements/section.php:365
#: includes/elements/section.php:424 includes/elements/section.php:452
#: includes/elements/section.php:492 includes/settings/settings.php:386
#: includes/widgets/common-base.php:231 includes/widgets/common-base.php:448
#: includes/widgets/divider.php:835 includes/widgets/heading.php:194
#: includes/widgets/icon-box.php:133 includes/widgets/icon-list.php:122
#: includes/widgets/icon.php:134 includes/widgets/image-carousel.php:167
#: includes/widgets/image-carousel.php:185
#: includes/widgets/image-carousel.php:376
#: includes/widgets/image-carousel.php:763
#: includes/widgets/image-gallery.php:210
#: includes/widgets/image-gallery.php:266
#: includes/widgets/image-gallery.php:289 includes/widgets/image.php:235
#: includes/widgets/image.php:382 includes/widgets/progress.php:157
#: includes/widgets/text-editor.php:181 includes/widgets/text-editor.php:424
#: includes/widgets/traits/button-trait.php:71
#: modules/element-cache/module.php:110
#: modules/floating-buttons/base/widget-contact-button-base.php:1211
#: modules/floating-buttons/base/widget-contact-button-base.php:1263
#: modules/floating-buttons/base/widget-contact-button-base.php:1350
#: modules/floating-buttons/base/widget-contact-button-base.php:1559
#: modules/floating-buttons/base/widget-contact-button-base.php:1725
#: modules/floating-buttons/base/widget-contact-button-base.php:2289
#: modules/floating-buttons/base/widget-contact-button-base.php:2615
#: modules/floating-buttons/base/widget-contact-button-base.php:2684
#: modules/floating-buttons/base/widget-contact-button-base.php:2815
#: modules/link-in-bio/base/widget-link-in-bio-base.php:179
#: modules/page-templates/module.php:301
#: modules/shapes/widgets/text-path.php:203 assets/js/editor.js:45574
#: assets/js/editor.js:45585
msgid "Default"
msgstr "پیش‌ فرض"

#: core/kits/documents/tabs/settings-lightbox.php:103
#: core/kits/documents/tabs/settings-lightbox.php:113
#: core/kits/documents/tabs/settings-lightbox.php:120
#: includes/compatibility.php:163 includes/widgets/alert.php:302
#: includes/widgets/icon-box.php:181 includes/widgets/icon-box.php:655
#: includes/widgets/image-box.php:156 includes/widgets/image-box.php:576
#: includes/widgets/image-carousel.php:396
#: modules/floating-buttons/base/widget-contact-button-base.php:2340
#: modules/link-in-bio/base/widget-link-in-bio-base.php:892
#: modules/link-in-bio/base/widget-link-in-bio-base.php:897
#: modules/link-in-bio/base/widget-link-in-bio-base.php:1348
msgid "Description"
msgstr "توضیحات"

#: includes/base/element-base.php:1348 includes/controls/dimensions.php:84
#: includes/elements/column.php:189 includes/elements/container.php:1176
#: includes/elements/container.php:1658 includes/elements/section.php:408
#: includes/elements/section.php:427 includes/elements/section.php:973
#: includes/widgets/common-base.php:575 includes/widgets/counter.php:355
#: includes/widgets/icon-box.php:277 includes/widgets/image-box.php:252
#: modules/floating-buttons/base/widget-contact-button-base.php:3000
#: modules/floating-buttons/base/widget-floating-bars-base.php:1441
#: assets/js/packages/editor-controls/editor-controls.js:10
#: assets/js/packages/editor-editing-panel/editor-editing-panel.js:8
#: assets/js/packages/editor-editing-panel/editor-editing-panel.strings.js:83
#: assets/js/packages/editor-editing-panel/editor-editing-panel.strings.js:132
msgid "Bottom"
msgstr "پائین"

#: includes/controls/groups/typography.php:113
msgctxt "Typography Control"
msgid "Size"
msgstr "سایز"

#: includes/controls/groups/typography.php:138
#: includes/controls/groups/typography.php:299
msgctxt "Typography Control"
msgid "Weight"
msgstr "وزن فونت"

#: includes/controls/image-dimensions.php:102
#: includes/editor-templates/panel.php:175
#: includes/editor-templates/templates.php:266
#: modules/history/views/revisions-panel-template.php:14
#: assets/js/editor.js:10196 assets/js/editor.js:38326
msgid "Apply"
msgstr "اعمال تغییرات"

#: includes/controls/repeater.php:178
#: modules/floating-buttons/base/widget-contact-button-base.php:1005
#: modules/floating-buttons/base/widget-floating-bars-base.php:367
#: modules/nested-accordion/widgets/nested-accordion.php:161
msgid "Add Item"
msgstr "افزودن آیتم"

#: includes/editor-templates/panel.php:69
#: includes/editor-templates/panel.php:70
msgid "Menu"
msgstr "منو"

#: includes/editor-templates/panel.php:74
#: includes/editor-templates/panel.php:75
msgid "Widgets Panel"
msgstr "پنل ابزارک ها"

#: includes/editor-templates/templates.php:144
#: includes/elements/container.php:1190 includes/elements/section.php:987
#: includes/template-library/sources/local.php:1706
#: includes/widgets/alert.php:118 includes/widgets/progress.php:154
#: includes/widgets/traits/button-trait.php:67
#: modules/floating-buttons/base/widget-floating-bars-base.php:563
#: modules/link-in-bio/base/widget-link-in-bio-base.php:1091
msgid "Type"
msgstr "نوع"

#: core/editor/loader/v1/templates/editor-body-v1-view.php:31
#: core/editor/loader/v2/templates/editor-body-v2-view.php:33
#: includes/editor-templates/templates.php:219 assets/js/ai-admin.js:7568
#: assets/js/ai-gutenberg.js:9416 assets/js/ai-layout.js:3049
#: assets/js/ai-media-library.js:9197 assets/js/ai-unify-product-images.js:9197
#: assets/js/ai.js:10644 assets/js/editor.js:28295
msgid "Preview"
msgstr "پیش‌نمایش"

#: includes/controls/groups/grid-container.php:42
#: includes/widgets/image-gallery.php:160 includes/widgets/social-icons.php:295
#: includes/widgets/text-editor.php:177
msgid "Columns"
msgstr "ستون‌ها"

#: includes/elements/section.php:78 modules/library/documents/section.php:43
#: assets/js/container-converter.js:95 assets/js/editor.js:10293
msgid "Section"
msgstr "بخش (سکشن)"

#: core/kits/documents/tabs/settings-layout.php:24
#: includes/elements/column.php:117 includes/elements/container.php:1360
#: includes/elements/section.php:231 includes/managers/controls.php:338
#: includes/managers/elements.php:284 includes/widgets/common-base.php:182
#: includes/widgets/icon-list.php:117
#: modules/floating-buttons/base/widget-contact-button-base.php:2926
#: modules/floating-buttons/base/widget-floating-bars-base.php:1425
#: modules/nested-accordion/widgets/nested-accordion.php:107
#: assets/js/editor.js:37166
#: assets/js/packages/editor-editing-panel/editor-editing-panel.js:8
#: assets/js/packages/editor-editing-panel/editor-editing-panel.strings.js:2
msgid "Layout"
msgstr "طرح‌بندی"

#: includes/controls/groups/flex-container.php:214
#: includes/controls/groups/grid-container.php:196
#: includes/controls/groups/grid-container.php:236
#: includes/elements/column.php:188 includes/elements/section.php:407
#: includes/elements/section.php:426 includes/widgets/counter.php:351
#: includes/widgets/icon-box.php:273 includes/widgets/image-box.php:248
#: modules/floating-buttons/base/widget-contact-button-base.php:2996
msgid "Middle"
msgstr "وسط"

#: core/kits/documents/tabs/theme-style-buttons.php:91
#: core/kits/documents/tabs/theme-style-form-fields.php:117
#: core/kits/documents/tabs/theme-style-images.php:63
#: core/kits/documents/tabs/theme-style-typography.php:119
#: includes/base/element-base.php:873
#: includes/controls/groups/typography.php:152
#: includes/controls/groups/typography.php:168
#: includes/controls/groups/typography.php:178 includes/elements/column.php:280
#: includes/elements/column.php:395 includes/elements/column.php:444
#: includes/elements/column.php:551 includes/elements/column.php:887
#: includes/elements/container.php:642 includes/elements/container.php:771
#: includes/elements/container.php:845 includes/elements/container.php:990
#: includes/elements/container.php:1829 includes/elements/section.php:543
#: includes/elements/section.php:654 includes/elements/section.php:718
#: includes/elements/section.php:841 includes/elements/section.php:1340
#: includes/widgets/alert.php:412 includes/widgets/common-base.php:744
#: includes/widgets/common-base.php:791 includes/widgets/common-base.php:866
#: includes/widgets/google-maps.php:221 includes/widgets/heading.php:312
#: includes/widgets/heading.php:344 includes/widgets/icon-box.php:392
#: includes/widgets/icon-list.php:426 includes/widgets/icon-list.php:655
#: includes/widgets/icon.php:213 includes/widgets/image-box.php:414
#: includes/widgets/image.php:434 includes/widgets/text-editor.php:336
#: includes/widgets/traits/button-trait.php:339
#: modules/floating-buttons/base/widget-contact-button-base.php:1199
#: modules/floating-buttons/base/widget-contact-button-base.php:1405
#: modules/floating-buttons/base/widget-contact-button-base.php:1977
#: modules/floating-buttons/base/widget-contact-button-base.php:2472
#: modules/floating-buttons/base/widget-contact-button-base.php:2603
#: modules/floating-buttons/base/widget-contact-button-base.php:2788
#: modules/floating-buttons/base/widget-floating-bars-base.php:656
#: modules/floating-buttons/base/widget-floating-bars-base.php:888
#: modules/floating-buttons/base/widget-floating-bars-base.php:1374
#: modules/nested-accordion/widgets/nested-accordion.php:671
#: modules/nested-accordion/widgets/nested-accordion.php:721
#: modules/nested-tabs/widgets/nested-tabs.php:493
#: modules/nested-tabs/widgets/nested-tabs.php:737
#: modules/nested-tabs/widgets/nested-tabs.php:937
#: modules/shapes/widgets/text-path.php:409
#: modules/shapes/widgets/text-path.php:501
msgid "Normal"
msgstr "عادی"

#: core/base/traits/shared-widget-controls-trait.php:156
#: includes/elements/column.php:541 includes/elements/container.php:977
#: includes/elements/section.php:831 includes/widgets/common-base.php:856
#: modules/floating-buttons/base/widget-floating-bars-base.php:761
#: assets/js/packages/editor-editing-panel/editor-editing-panel.js:8
#: assets/js/packages/editor-editing-panel/editor-editing-panel.strings.js:8
#: assets/js/packages/editor-editing-panel/editor-editing-panel.strings.js:150
msgid "Border"
msgstr "حاشیه"

#: includes/elements/container.php:381 includes/elements/section.php:253
msgid "Boxed"
msgstr "جعبه ای"

#: includes/elements/container.php:382 includes/elements/section.php:254
#: includes/widgets/common-base.php:232 includes/widgets/icon-list.php:216
#: modules/link-in-bio/base/widget-link-in-bio-base.php:1551
msgid "Full Width"
msgstr "تمام عرض"

#: includes/elements/column.php:918 includes/elements/container.php:1867
#: includes/elements/section.php:1372 includes/managers/controls.php:337
#: includes/widgets/common-base.php:1220
#: modules/floating-buttons/base/widget-contact-button-base.php:3059
#: modules/floating-buttons/base/widget-floating-bars-base.php:1467
msgid "Responsive"
msgstr "واکنش‌گرا"

#: includes/controls/groups/flex-container.php:145
#: includes/controls/groups/flex-item.php:67
#: includes/controls/groups/grid-container.php:147
#: includes/controls/groups/grid-container.php:175
#: includes/elements/section.php:405 includes/widgets/counter.php:404
#: includes/widgets/tabs.php:221 includes/widgets/tabs.php:251
#: includes/widgets/traits/button-trait.php:267
#: modules/floating-buttons/base/widget-floating-bars-base.php:1201
#: modules/nested-accordion/widgets/nested-accordion.php:185
#: modules/nested-tabs/widgets/nested-tabs.php:246
#: modules/nested-tabs/widgets/nested-tabs.php:288
#: assets/js/packages/editor-editing-panel/editor-editing-panel.js:8
#: assets/js/packages/editor-editing-panel/editor-editing-panel.strings.js:123
#: assets/js/packages/editor-editing-panel/editor-editing-panel.strings.js:128
msgid "Stretch"
msgstr "کشیده"

#: core/settings/editor-preferences/model.php:185
#: includes/base/element-base.php:1389
#: includes/controls/groups/grid-container.php:34
#: includes/widgets/alert.php:164 includes/widgets/audio.php:162
#: includes/widgets/audio.php:173 includes/widgets/audio.php:184
#: includes/widgets/audio.php:195 includes/widgets/audio.php:209
#: includes/widgets/audio.php:220 includes/widgets/audio.php:231
#: includes/widgets/audio.php:242 includes/widgets/counter.php:203
#: includes/widgets/progress.php:192 includes/widgets/video.php:389
#: includes/widgets/video.php:404 includes/widgets/video.php:431
#: includes/widgets/video.php:509 includes/widgets/video.php:523
#: includes/widgets/video.php:537 includes/widgets/video.php:563
#: includes/widgets/video.php:632 includes/widgets/video.php:673
#: modules/floating-buttons/base/widget-contact-button-base.php:519
#: modules/floating-buttons/base/widget-contact-button-base.php:629
#: modules/floating-buttons/base/widget-contact-button-base.php:694
#: modules/floating-buttons/base/widget-contact-button-base.php:2526
#: modules/floating-buttons/base/widget-floating-bars-base.php:231
#: modules/floating-buttons/base/widget-floating-bars-base.php:298
msgid "Hide"
msgstr "پنهان"

#: modules/floating-buttons/base/widget-contact-button-base.php:1207
#: modules/floating-buttons/base/widget-contact-button-base.php:1259
#: modules/floating-buttons/base/widget-contact-button-base.php:1346
#: modules/floating-buttons/base/widget-contact-button-base.php:1555
#: modules/floating-buttons/base/widget-contact-button-base.php:1721
#: modules/floating-buttons/base/widget-contact-button-base.php:2611
#: modules/floating-buttons/base/widget-contact-button-base.php:2680
#: assets/js/styleguide-app.a6e297c616479b98c03d.bundle.js:444
msgid "Colors"
msgstr "رنگ‌بندی"

#: core/kits/documents/tabs/global-colors.php:82
#: core/kits/documents/tabs/global-typography.php:135
msgid "Secondary"
msgstr "رنگ دوم"

#: includes/settings/settings.php:271
msgid "Post Types"
msgstr "انواع نوشته"

#: includes/widgets/divider.php:832 includes/widgets/icon-box.php:130
#: includes/widgets/icon.php:131 includes/widgets/text-editor.php:421
msgid "View"
msgstr "نمایش"

#: core/base/traits/shared-widget-controls-trait.php:207
#: includes/controls/groups/border.php:90 includes/widgets/accordion.php:303
#: includes/widgets/alert.php:229 includes/widgets/social-icons.php:562
#: includes/widgets/tabs.php:334 includes/widgets/toggle.php:305
#: includes/widgets/traits/button-trait.php:429
#: modules/floating-buttons/base/widget-floating-bars-base.php:722
#: modules/floating-buttons/base/widget-floating-bars-base.php:814
#: modules/nested-accordion/widgets/nested-accordion.php:514
#: modules/nested-tabs/widgets/nested-tabs.php:522
#: modules/nested-tabs/widgets/nested-tabs.php:580
#: modules/nested-tabs/widgets/nested-tabs.php:664
#: modules/nested-tabs/widgets/nested-tabs.php:1016
#: assets/js/packages/editor-editing-panel/editor-editing-panel.js:8
#: assets/js/packages/editor-editing-panel/editor-editing-panel.strings.js:151
msgid "Border Color"
msgstr "رنگ حاشیه"

#: includes/widgets/alert.php:123 includes/widgets/progress.php:159
#: includes/widgets/traits/button-trait.php:73
msgid "Success"
msgstr "موفق"

#: includes/widgets/heading.php:195 includes/widgets/traits/button-trait.php:35
#: modules/floating-buttons/base/widget-contact-button-base.php:1118
#: modules/floating-buttons/base/widget-contact-button-base.php:1543
#: modules/floating-buttons/base/widget-contact-button-base.php:1925
#: modules/floating-buttons/base/widget-contact-button-base.php:2275
#: modules/link-in-bio/base/widget-link-in-bio-base.php:1404
msgid "Small"
msgstr "کوچک"

#: includes/widgets/heading.php:196 includes/widgets/traits/button-trait.php:36
#: modules/floating-buttons/base/widget-contact-button-base.php:1119
#: modules/floating-buttons/base/widget-contact-button-base.php:1544
#: modules/floating-buttons/base/widget-contact-button-base.php:1926
#: modules/floating-buttons/base/widget-contact-button-base.php:2276
#: modules/link-in-bio/base/widget-link-in-bio-base.php:1405
msgid "Medium"
msgstr "میانه"

#: includes/elements/column.php:741 includes/elements/section.php:1209
#: includes/widgets/heading.php:267 includes/widgets/icon-box.php:309
#: includes/widgets/image-box.php:284 includes/widgets/image-carousel.php:849
#: includes/widgets/image-gallery.php:383 includes/widgets/image.php:593
#: includes/widgets/star-rating.php:231 includes/widgets/text-editor.php:274
msgid "Justified"
msgstr "تراز شده"

#: includes/widgets/divider.php:46 includes/widgets/divider.php:378
#: includes/widgets/divider.php:528 includes/widgets/divider.php:578
#: includes/widgets/icon-list.php:283
msgid "Divider"
msgstr "جداکننده"

#: includes/controls/groups/border.php:65 includes/widgets/divider.php:342
#: includes/widgets/icon-list.php:300 includes/widgets/star-rating.php:188
#: assets/js/packages/editor-editing-panel/editor-editing-panel.js:8
#: assets/js/packages/editor-editing-panel/editor-editing-panel.strings.js:136
msgid "Solid"
msgstr "یکپارچه"

#: includes/controls/groups/border.php:66 includes/widgets/divider.php:343
#: includes/widgets/icon-list.php:301
#: assets/js/packages/editor-editing-panel/editor-editing-panel.js:8
#: assets/js/packages/editor-editing-panel/editor-editing-panel.strings.js:139
msgid "Double"
msgstr "دوبل"

#: includes/controls/groups/border.php:68 includes/widgets/divider.php:345
#: includes/widgets/icon-list.php:303
#: assets/js/packages/editor-editing-panel/editor-editing-panel.js:8
#: assets/js/packages/editor-editing-panel/editor-editing-panel.strings.js:137
msgid "Dashed"
msgstr "خط فاصله دار"

#: includes/widgets/divider.php:605 includes/widgets/icon-list.php:319
#: modules/floating-buttons/base/widget-contact-button-base.php:2550
#: modules/link-in-bio/base/widget-link-in-bio-base.php:1215
msgid "Weight"
msgstr "وزن فونت"

#: includes/widgets/google-maps.php:44 includes/widgets/google-maps.php:125
#: includes/widgets/google-maps.php:212
msgid "Google Maps"
msgstr "نقشه گوگل"

#: includes/widgets/icon-box.php:45 includes/widgets/icon-box.php:110
msgid "Icon Box"
msgstr "آیکن جعبه"

#: includes/widgets/common-base.php:135 includes/widgets/icon-box.php:153
#: includes/widgets/icon.php:151 includes/widgets/social-icons.php:286
#: modules/link-in-bio/base/widget-link-in-bio-base.php:1675
#: modules/shapes/module.php:45
msgid "Circle"
msgstr "گرد"

#: includes/widgets/icon-box.php:151 includes/widgets/icon.php:149
#: includes/widgets/social-icons.php:284
#: modules/link-in-bio/base/widget-link-in-bio-base.php:1676
#: assets/js/ai-admin.js:11313 assets/js/ai-gutenberg.js:13161
#: assets/js/ai-media-library.js:12942
#: assets/js/ai-unify-product-images.js:12942 assets/js/ai.js:14389
msgid "Square"
msgstr "مربعی"

#: includes/base/element-base.php:880 includes/base/element-base.php:892
#: includes/widgets/divider.php:981 includes/widgets/icon-box.php:541
#: includes/widgets/icon.php:371 modules/shapes/widgets/text-path.php:283
msgid "Rotate"
msgstr "چرخش"

#: includes/widgets/icon-list.php:185
msgid "List Item #1"
msgstr "آیتم #1 در لیست"

#: includes/widgets/icon-list.php:192
msgid "List Item #2"
msgstr "آیتم #2 در لیست"

#: includes/widgets/icon-list.php:199
msgid "List Item #3"
msgstr "آیتم #3 در لیست"

#: core/settings/editor-preferences/model.php:216
#: includes/widgets/image-carousel.php:211
#: includes/widgets/image-carousel.php:549
msgid "Navigation"
msgstr "ناوبری"

#: includes/widgets/image-carousel.php:216
#: includes/widgets/image-carousel.php:560
msgid "Arrows"
msgstr "پیکان‌ها"

#: includes/widgets/image-gallery.php:191
msgid "Attachment Page"
msgstr "برگه ضمیمه"

#: includes/widgets/menu-anchor.php:121
msgid "This ID will be the CSS ID you will have to use in your own page, Without #."
msgstr "این همان شناسه مورد استفاده در CSS خواهد بود که شما در برگه خود استفاده خواهید کرد. البته بدون #."

#: includes/widgets/menu-anchor.php:120
msgid "For Example: About"
msgstr "برای مثال: About"

#: includes/widgets/progress.php:189
msgid "Display Percentage"
msgstr "نمایش درصد"

#: includes/widgets/progress.php:206
msgid "e.g. Web Designer"
msgstr "به عنوان مثال: طراح وب"

#: includes/widgets/progress.php:315
msgid "Title Style"
msgstr "استایل عنوان"

#: includes/widgets/sidebar.php:42 includes/widgets/sidebar.php:106
msgid "Sidebar"
msgstr "سایدبار"

#: includes/widgets/sidebar.php:93 includes/widgets/sidebar.php:113
msgid "Choose Sidebar"
msgstr "یک ستون کناری انتخاب کنید"

#: includes/widgets/social-icons.php:523
msgid "Icon Hover"
msgstr "هاور آیکن"

#: includes/widgets/tabs.php:129 includes/widgets/tabs.php:130
#: modules/nested-tabs/widgets/nested-tabs.php:115
#: modules/nested-tabs/widgets/nested-tabs.php:116
msgid "Tab Title"
msgstr "عنوان زبانه"

#: includes/widgets/tabs.php:142 includes/widgets/tabs.php:143
msgid "Tab Content"
msgstr "محتوای زبانه"

#: includes/widgets/tabs.php:164
#: modules/nested-tabs/widgets/nested-tabs.php:167
msgid "Tabs Items"
msgstr "آیتم های زبانه"

#: includes/widgets/tabs.php:169
#: modules/nested-tabs/widgets/nested-tabs.php:172
msgid "Tab #1"
msgstr "زبانه #1"

#: includes/widgets/tabs.php:173
#: modules/nested-tabs/widgets/nested-tabs.php:175
msgid "Tab #2"
msgstr "زبانه #2"

#: modules/link-in-bio/base/widget-link-in-bio-base.php:1648
msgid "Image Size"
msgstr "اندازه عکس"

#: includes/widgets/text-editor.php:46 includes/widgets/text-editor.php:133
#: includes/widgets/text-editor.php:250
msgid "Text Editor"
msgstr "ویرایشگر متن"

#: includes/widgets/toggle.php:168
msgid "Toggle Items"
msgstr "آیتم های تغییر وضعیت"

#: includes/widgets/toggle.php:173
msgid "Toggle #1"
msgstr "تغییر وضعیت  #1"

#: includes/widgets/toggle.php:177
msgid "Toggle #2"
msgstr "تغییر وضعیت #2"

#: includes/widgets/video.php:491
msgid "Suggested Videos"
msgstr "ویدیوهای پیشنهادی"

#: includes/widgets/video.php:747
msgid "Aspect Ratio"
msgstr "نسبت ابعادی"

#: modules/gutenberg/module.php:98
msgid "&#8592; Back to WordPress Editor"
msgstr "&#8594; بازگشت به ویرایشگر وردپرس"

#: core/common/modules/finder/categories/site.php:64
msgid "Themes"
msgstr "پوسته ها"

#: modules/system-info/module.php:202
msgid "You do not have permission to download this file."
msgstr "شما دسترسی لازم برای دانلود این فایل را ندارید."

#: core/experiments/manager.php:397 core/experiments/manager.php:687
#: modules/element-cache/module.php:111 assets/js/editor.js:28088
#: assets/js/element-manager-admin.js:2224
msgid "Inactive"
msgstr "غیرفعال"

#: core/experiments/manager.php:396 core/experiments/manager.php:686
#: modules/element-cache/module.php:112
#: modules/floating-buttons/base/widget-contact-button-base.php:1310
#: modules/nested-accordion/widgets/nested-accordion.php:667
#: modules/nested-accordion/widgets/nested-accordion.php:730
#: modules/nested-tabs/widgets/nested-tabs.php:629
#: modules/nested-tabs/widgets/nested-tabs.php:809
#: modules/nested-tabs/widgets/nested-tabs.php:971 assets/js/editor.js:28090
#: assets/js/element-manager-admin.js:2221
msgid "Active"
msgstr "فعال"

#: includes/elements/column.php:821 includes/elements/container.php:1776
#: includes/elements/section.php:1294 includes/widgets/common-base.php:692
#: modules/floating-buttons/base/widget-contact-button-base.php:3105
#: modules/floating-buttons/base/widget-floating-bars-base.php:1513
#: assets/js/packages/editor-editing-panel/editor-editing-panel.js:8
#: assets/js/packages/editor-editing-panel/editor-editing-panel.strings.js:15
msgid "CSS Classes"
msgstr "کلاس های CSS"

#: core/kits/views/trash-kit-confirmation.php:30
#: includes/editor-templates/hotkeys.php:66
#: includes/editor-templates/templates.php:250 assets/js/editor.js:10563
#: assets/js/editor.js:10644 assets/js/editor.js:28356
#: assets/js/editor.js:30808 assets/js/editor.js:47750
#: assets/js/import-export-admin.js:272
#: assets/js/packages/editor-site-navigation/editor-site-navigation.js:2
#: assets/js/packages/editor-site-navigation/editor-site-navigation.strings.js:17
#: assets/js/packages/editor-site-navigation/editor-site-navigation.strings.js:25
msgid "Delete"
msgstr "حذف"

#: core/common/modules/finder/categories/edit.php:30
#: includes/controls/popover-toggle.php:68 assets/js/ai-admin.js:2334
#: assets/js/ai-admin.js:10205 assets/js/ai-admin.js:10212
#: assets/js/ai-gutenberg.js:4102 assets/js/ai-gutenberg.js:12053
#: assets/js/ai-gutenberg.js:12060 assets/js/ai-media-library.js:3963
#: assets/js/ai-media-library.js:11834 assets/js/ai-media-library.js:11841
#: assets/js/ai-unify-product-images.js:3963
#: assets/js/ai-unify-product-images.js:11834
#: assets/js/ai-unify-product-images.js:11841 assets/js/ai.js:4742
#: assets/js/ai.js:13281 assets/js/ai.js:13288
#: assets/js/element-manager-admin.js:2500
#: assets/js/element-manager-admin.js:2556
msgid "Edit"
msgstr "ویرایش"

#: includes/widgets/alert.php:136 includes/widgets/heading.php:169
#: includes/widgets/icon-box.php:173 includes/widgets/image-box.php:148
#: includes/widgets/progress.php:122
msgid "Enter your title"
msgstr "عنوان خود را وارد کنید"

#: includes/widgets/heading.php:47 includes/widgets/heading.php:154
#: includes/widgets/heading.php:243
#: modules/link-in-bio/base/widget-link-in-bio-base.php:846
#: modules/link-in-bio/base/widget-link-in-bio-base.php:851
#: modules/link-in-bio/base/widget-link-in-bio-base.php:1292
msgid "Heading"
msgstr "سربرگ"

#: includes/elements/column.php:677 includes/elements/section.php:1145
msgid "Heading Color"
msgstr "رنگ سربرگ"

#: includes/widgets/alert.php:151
msgid "I am a description. Click the edit button to change this text."
msgstr "من یک متن توضیحی هستم. با استفاده از کلید ویرایش می توانید متن را تغییر دهید."

#: includes/widgets/image-carousel.php:510
msgid "Slide"
msgstr "اسلاید"

#: includes/widgets/menu-anchor.php:115
msgid "The ID of Menu Anchor."
msgstr "شناسه لنگر فهرست."

#: core/settings/editor-preferences/model.php:184
#: includes/base/element-base.php:1390
#: includes/controls/groups/grid-container.php:33
#: includes/widgets/alert.php:163 includes/widgets/audio.php:163
#: includes/widgets/audio.php:174 includes/widgets/audio.php:185
#: includes/widgets/audio.php:196 includes/widgets/audio.php:210
#: includes/widgets/audio.php:221 includes/widgets/audio.php:232
#: includes/widgets/audio.php:243 includes/widgets/counter.php:202
#: includes/widgets/progress.php:191 includes/widgets/video.php:390
#: includes/widgets/video.php:405 includes/widgets/video.php:432
#: includes/widgets/video.php:510 includes/widgets/video.php:524
#: includes/widgets/video.php:538 includes/widgets/video.php:564
#: includes/widgets/video.php:633 includes/widgets/video.php:674
#: modules/floating-buttons/base/widget-contact-button-base.php:518
#: modules/floating-buttons/base/widget-contact-button-base.php:628
#: modules/floating-buttons/base/widget-contact-button-base.php:693
#: modules/floating-buttons/base/widget-contact-button-base.php:2525
#: modules/floating-buttons/base/widget-floating-bars-base.php:230
#: modules/floating-buttons/base/widget-floating-bars-base.php:297
#: assets/js/element-manager-admin.js:2076
msgid "Show"
msgstr "نمایش"

#: includes/controls/groups/flex-item.php:125 includes/widgets/alert.php:353
#: includes/widgets/common-base.php:1041 includes/widgets/divider.php:639
#: includes/widgets/divider.php:847 includes/widgets/heading.php:191
#: includes/widgets/icon-box.php:481 includes/widgets/icon-list.php:492
#: includes/widgets/icon.php:306 includes/widgets/image-carousel.php:589
#: includes/widgets/image-carousel.php:673 includes/widgets/rating.php:60
#: includes/widgets/social-icons.php:403 includes/widgets/star-rating.php:327
#: includes/widgets/text-editor.php:474
#: includes/widgets/traits/button-trait.php:114 includes/widgets/video.php:825
#: modules/floating-buttons/base/widget-contact-button-base.php:1114
#: modules/floating-buttons/base/widget-contact-button-base.php:1539
#: modules/floating-buttons/base/widget-contact-button-base.php:1921
#: modules/floating-buttons/base/widget-contact-button-base.php:2271
#: modules/floating-buttons/base/widget-floating-bars-base.php:490
#: modules/floating-buttons/base/widget-floating-bars-base.php:1088
#: modules/floating-buttons/base/widget-floating-bars-base.php:1313
#: modules/link-in-bio/base/widget-link-in-bio-base.php:1400
#: modules/nested-accordion/widgets/nested-accordion.php:603
#: modules/nested-tabs/widgets/nested-tabs.php:895
#: modules/shapes/widgets/text-path.php:251
#: assets/js/packages/editor-controls/editor-controls.js:10
#: assets/js/packages/editor-editing-panel/editor-editing-panel.js:8
#: assets/js/packages/editor-editing-panel/editor-editing-panel.strings.js:4
#: assets/js/packages/editor-editing-panel/editor-editing-panel.strings.js:101
msgid "Size"
msgstr "اندازه"

#: includes/base/element-base.php:1340 includes/controls/dimensions.php:82
#: includes/elements/column.php:187 includes/elements/container.php:1175
#: includes/elements/container.php:1654 includes/elements/section.php:406
#: includes/elements/section.php:425 includes/elements/section.php:972
#: includes/widgets/common-base.php:571 includes/widgets/counter.php:347
#: includes/widgets/icon-box.php:247 includes/widgets/icon-box.php:269
#: includes/widgets/image-box.php:221 includes/widgets/image-box.php:244
#: includes/widgets/testimonial.php:205 includes/widgets/video.php:967
#: modules/floating-buttons/base/widget-contact-button-base.php:2992
#: modules/floating-buttons/base/widget-floating-bars-base.php:1437
#: assets/js/packages/editor-controls/editor-controls.js:10
#: assets/js/packages/editor-editing-panel/editor-editing-panel.js:8
#: assets/js/packages/editor-editing-panel/editor-editing-panel.strings.js:81
#: assets/js/packages/editor-editing-panel/editor-editing-panel.strings.js:130
msgid "Top"
msgstr "بالا"

#: core/kits/documents/tabs/theme-style-typography.php:18
#: core/kits/documents/tabs/theme-style-typography.php:37
#: includes/controls/groups/typography.php:436 includes/elements/column.php:668
#: includes/elements/section.php:1137 assets/js/editor-modules.js:1430
#: assets/js/editor.js:42844
#: assets/js/packages/editor-editing-panel/editor-editing-panel.js:8
#: assets/js/packages/editor-editing-panel/editor-editing-panel.strings.js:6
msgid "Typography"
msgstr "تایپوگرافی"

#: modules/system-info/module.php:166
msgid "You can copy the below info as simple text with Ctrl+C / Ctrl+V:"
msgstr "شما می توانید اطلاعات زیر را در قالب یک متن ساده و با استفاده از کلیدهای ترکیبی  Ctrl+C / Ctrl+V کپی کنید:"

#: includes/elements/column.php:701 includes/elements/section.php:1169
#: includes/widgets/heading.php:374 includes/widgets/text-editor.php:358
#: includes/widgets/text-editor.php:378
msgid "Link Color"
msgstr "رنگ لینک"

#: core/kits/documents/tabs/settings-lightbox.php:101
#: core/kits/documents/tabs/settings-lightbox.php:118
#: includes/widgets/image-carousel.php:389
#: includes/widgets/image-carousel.php:395
#: includes/widgets/image-carousel.php:822
#: includes/widgets/image-gallery.php:170
#: includes/widgets/image-gallery.php:356 includes/widgets/image.php:158
#: includes/widgets/image.php:565
msgid "Caption"
msgstr "زیرنویس"

#. Author of the plugin
#: elementor.php
msgid "Elementor.com"
msgstr "Elementor.com"

#: core/document-types/post.php:65
msgid "Posts"
msgstr "پست ها"

#: core/document-types/page-base.php:144
#: core/kits/documents/tabs/theme-style-buttons.php:243
#: core/kits/documents/tabs/theme-style-form-fields.php:159
#: includes/elements/column.php:779 includes/elements/container.php:1380
#: includes/elements/section.php:1252 includes/widgets/accordion.php:394
#: includes/widgets/accordion.php:541 includes/widgets/common-base.php:212
#: includes/widgets/divider.php:870 includes/widgets/icon-box.php:499
#: includes/widgets/icon.php:343 includes/widgets/social-icons.php:422
#: includes/widgets/toggle.php:418 includes/widgets/toggle.php:565
#: includes/widgets/traits/button-trait.php:501
#: modules/floating-buttons/base/widget-contact-button-base.php:1459
#: modules/floating-buttons/base/widget-contact-button-base.php:2184
#: modules/floating-buttons/base/widget-contact-button-base.php:2199
#: modules/floating-buttons/base/widget-contact-button-base.php:2747
#: modules/floating-buttons/base/widget-contact-button-base.php:2887
#: modules/floating-buttons/base/widget-floating-bars-base.php:857
#: modules/floating-buttons/base/widget-floating-bars-base.php:1241
#: modules/link-in-bio/base/widget-link-in-bio-base.php:1176
#: modules/nested-accordion/widgets/nested-accordion.php:474
#: modules/nested-accordion/widgets/nested-accordion.php:538
#: modules/nested-tabs/widgets/nested-tabs.php:701
#: modules/nested-tabs/widgets/nested-tabs.php:1051
#: assets/js/packages/editor-editing-panel/editor-editing-panel.js:8
#: assets/js/packages/editor-editing-panel/editor-editing-panel.strings.js:62
msgid "Padding"
msgstr "فاصله داخلی"

#: core/document-types/page-base.php:132 includes/elements/column.php:766
#: includes/elements/container.php:1368 includes/elements/section.php:1233
#: includes/widgets/common-base.php:200
#: assets/js/packages/editor-editing-panel/editor-editing-panel.js:8
#: assets/js/packages/editor-editing-panel/editor-editing-panel.strings.js:63
msgid "Margin"
msgstr "حاشیه‌خارجی"

#: includes/widgets/google-maps.php:150
msgid "London Eye, London, United Kingdom"
msgstr "London Eye، لندن، انگلستان"

#: includes/widgets/image-carousel.php:445
msgid "Pause on Hover"
msgstr "توقف هنگام هاور"

#: modules/nested-tabs/widgets/nested-tabs.php:230
#: modules/nested-tabs/widgets/nested-tabs.php:272
#: assets/js/packages/editor-editing-panel/editor-editing-panel.js:8
#: assets/js/packages/editor-editing-panel/editor-editing-panel.strings.js:50
msgid "Justify"
msgstr "ترازبندی"

#: includes/editor-templates/panel-elements.php:74
msgid "Search Widget..."
msgstr "جستجوی ابزارک..."

#: includes/managers/elements.php:351
msgid "WordPress"
msgstr "وردپرس"

#: includes/managers/elements.php:288
msgid "Basic"
msgstr "ویجت‌های پایه"

#: includes/widgets/accordion.php:46 includes/widgets/accordion.php:123
#: includes/widgets/accordion.php:273
#: modules/nested-accordion/widgets/nested-accordion.php:39
#: modules/nested-accordion/widgets/nested-accordion.php:393
msgid "Accordion"
msgstr "آکاردیون"

#: includes/widgets/accordion.php:165
msgid "Accordion Items"
msgstr "آیتم‌های آکاردیون"

#: includes/widgets/accordion.php:134
msgid "Accordion Title"
msgstr "عنوان آکاردیون"

#: includes/widgets/accordion.php:174
msgid "Accordion #2"
msgstr "آکاردیون #2"

#: includes/widgets/accordion.php:170
msgid "Accordion #1"
msgstr "آکاردیون #1"

#: includes/widgets/menu-anchor.php:43 includes/widgets/menu-anchor.php:108
msgid "Menu Anchor"
msgstr "لنگر منو"

#: includes/widgets/tabs.php:46 includes/widgets/tabs.php:118
#: includes/widgets/tabs.php:267 modules/nested-tabs/widgets/nested-tabs.php:34
#: modules/nested-tabs/widgets/nested-tabs.php:107
#: modules/nested-tabs/widgets/nested-tabs.php:444
msgid "Tabs"
msgstr "تب‌ها"

#: core/breakpoints/manager.php:319
msgid "Mobile Landscape"
msgstr "حالت افقی موبایل"

#: core/breakpoints/manager.php:314
msgid "Mobile Portrait"
msgstr "حالت عمودی موبایل"

#: core/kits/documents/tabs/theme-style-buttons.php:152
#: core/kits/documents/tabs/theme-style-buttons.php:227
#: core/kits/documents/tabs/theme-style-form-fields.php:233
#: core/kits/documents/tabs/theme-style-images.php:83
#: core/kits/documents/tabs/theme-style-images.php:154
#: includes/elements/column.php:566 includes/elements/column.php:603
#: includes/elements/container.php:1022 includes/elements/container.php:1073
#: includes/elements/section.php:855 includes/elements/section.php:891
#: includes/widgets/common-base.php:881 includes/widgets/common-base.php:918
#: includes/widgets/divider.php:1025 includes/widgets/icon-box.php:578
#: includes/widgets/icon.php:407 includes/widgets/image-box.php:399
#: includes/widgets/image-carousel.php:808
#: includes/widgets/image-gallery.php:342 includes/widgets/image.php:540
#: includes/widgets/progress.php:262 includes/widgets/social-icons.php:509
#: includes/widgets/testimonial.php:344 includes/widgets/text-editor.php:529
#: includes/widgets/traits/button-trait.php:488
#: modules/nested-accordion/widgets/nested-accordion.php:461
#: modules/nested-accordion/widgets/nested-accordion.php:526
#: modules/nested-tabs/widgets/nested-tabs.php:688
#: modules/nested-tabs/widgets/nested-tabs.php:1028
#: assets/js/packages/editor-editing-panel/editor-editing-panel.js:8
#: assets/js/packages/editor-editing-panel/editor-editing-panel.strings.js:149
msgid "Border Radius"
msgstr "انحنای حاشیه"

#: core/admin/admin-notices.php:476 includes/controls/gallery.php:123
#: includes/controls/media.php:319
msgid "Activate Plugin"
msgstr "فعال‌کردن افزونه"

#: includes/widgets/divider.php:699 includes/widgets/icon-list.php:519
#: includes/widgets/image-gallery.php:286 includes/widgets/star-rating.php:291
msgid "Gap"
msgstr "شکاف"

#: includes/elements/section.php:288
msgid "No Gap"
msgstr "‫بدون شکاف"

#: includes/elements/section.php:283 includes/widgets/text-editor.php:202
msgid "Columns Gap"
msgstr "شکاف ستون‌ها"

#: includes/widgets/counter.php:491
#: modules/floating-buttons/base/widget-contact-button-base.php:333
#: modules/floating-buttons/base/widget-contact-button-base.php:885
#: modules/link-in-bio/base/widget-link-in-bio-base.php:481
#: modules/link-in-bio/base/widget-link-in-bio-base.php:728
msgid "Number"
msgstr "عدد"

#: includes/widgets/accordion.php:419 includes/widgets/divider.php:470
#: includes/widgets/heading.php:251 includes/widgets/icon-box.php:293
#: includes/widgets/icon-list.php:260 includes/widgets/icon.php:185
#: includes/widgets/image-box.php:268 includes/widgets/image-carousel.php:833
#: includes/widgets/image-gallery.php:367 includes/widgets/image.php:259
#: includes/widgets/image.php:577 includes/widgets/rating.php:199
#: includes/widgets/social-icons.php:324 includes/widgets/star-rating.php:215
#: includes/widgets/tabs.php:205 includes/widgets/tabs.php:235
#: includes/widgets/tabs.php:422 includes/widgets/testimonial.php:221
#: includes/widgets/text-editor.php:258 includes/widgets/toggle.php:443
#: includes/widgets/traits/button-trait.php:283
#: modules/shapes/widgets/text-path.php:172
#: assets/js/packages/editor-editing-panel/editor-editing-panel.js:8
#: assets/js/packages/editor-editing-panel/editor-editing-panel.strings.js:51
msgid "Alignment"
msgstr "ترازبندی"

#: includes/elements/column.php:725 includes/elements/section.php:1193
msgid "Text Align"
msgstr "تراز متن"

#: core/base/traits/shared-widget-controls-trait.php:179
#: includes/controls/groups/border.php:77 includes/widgets/accordion.php:281
#: includes/widgets/divider.php:1002 includes/widgets/icon-box.php:563
#: includes/widgets/icon.php:392 includes/widgets/tabs.php:311
#: includes/widgets/text-editor.php:551 includes/widgets/toggle.php:284
#: modules/floating-buttons/base/widget-floating-bars-base.php:777
#: modules/nested-accordion/widgets/nested-accordion.php:517
#: modules/nested-tabs/widgets/nested-tabs.php:525
#: modules/nested-tabs/widgets/nested-tabs.php:583
#: modules/nested-tabs/widgets/nested-tabs.php:667
#: modules/nested-tabs/widgets/nested-tabs.php:1019
#: assets/js/packages/editor-editing-panel/editor-editing-panel.js:8
#: assets/js/packages/editor-editing-panel/editor-editing-panel.strings.js:134
msgid "Border Width"
msgstr "عرض حاشیه"