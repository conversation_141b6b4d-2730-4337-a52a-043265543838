{"translation-revision-date": "2025-04-09 18:06:38+0000", "generator": "GlotPress/4.0.1", "domain": "messages", "locale_data": {"messages": {"": {"domain": "messages", "plural-forms": "nplurals=1; plural=0;", "lang": "fa"}, "Product counts": ["تعداد محصول"], "Show results for any of the attributes selected (displayed products don’t have to have them all).": ["نمایش نتایج برای هر یک از ویژگی‌های انتخاب شده (محصولات نمایش داده شده الزاما همه آنها را ندارد)."], "Enable customers to filter the product collection by selecting one or more %s attributes.": ["مشتریان را قادر می‌سازد تا مجموعه محصول را با انتخاب یک یا چند ویژگی %s فیلتر کنند."], "Name, A to Z": ["نام، (الف تا ی)"], "Red": ["قرمز"], "Green": ["سبز"], "Gray": ["خاکستری"], "Least results first": ["ابتدا کمترین نتایج"], "Most results first": ["ابتدا بیشترین نتایج"], "Choose the attribute to show in this filter.": ["ویژگی را برای نمایش در این فیلتر انتخاب کنید."], "Determine the order of filter options.": ["ترتیب گزینه‌های فیلتر را تعیین کنید."], "Show results for <b>all</b> selected attributes. Displayed products must contain <b>all of them</b> to appear in the results.": ["نمایش نتایج برای <b>همه</b> ویژگی‌های انتخاب شده. محصولات نمایش داده شده باید حاوی <b>همه آنها</b> باشند تا در نتایج ظاهر شوند."], "Name, Z to A": ["نام، (ی تا الف)"], "Logic": ["منطق"], "Sort order": ["نحوه مرتب‌سازی"], "There are no products with the selected attributes.": ["هیچ محصولی با ویژگی‌های انتخاب شده وجود ندارد."], "Please select an attribute to use this filter!": ["لطفاً یک ویژگی را برای استفاده از این فیلتر انتخاب کنید!"], "Select an option": ["گزینه‌ای را برگزینید"], "Yellow": ["زرد"], "Blue": ["آ<PERSON><PERSON>"], "Attributes are needed for filtering your products. You haven't created any attributes yet.": ["ویژگی‌ها برای محدود کردن محصولاتتان مورد نیاز است. شما هنوز هیچ ویژگی‌ای ایجاد نکرده‌اید."], "Attribute": ["خاصیت"], "Display": ["نمایش"], "Any": ["همه"], "All": ["همه"], "Settings": ["پیکربندی"]}}, "comment": {"reference": "assets/client/blocks/product-filter-attribute.js"}}