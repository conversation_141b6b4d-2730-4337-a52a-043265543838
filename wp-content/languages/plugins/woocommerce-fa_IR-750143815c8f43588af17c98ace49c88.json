{"translation-revision-date": "2025-04-09 18:06:38+0000", "generator": "GlotPress/4.0.1", "domain": "messages", "locale_data": {"messages": {"": {"domain": "messages", "plural-forms": "nplurals=1; plural=0;", "lang": "fa"}, "Always (even if empty)": ["همیشه (حتی اگر خالی باشد)"], "Only if cart has items": ["فقط در صورتی که مواردی در سبد خرید باشد"], "Show Cart Item Count:": ["نمایش تعداد اقلام سبد خرید:"], "The editor does not display the real count value, but a placeholder to indicate how it will look on the front-end.": ["ویرایشگر، مقدار واقعی شمارش را نمایش نمی‌دهد، بلکه یک جایگزین را نمایش می‌دهد تا نشان دهد که در قسمت جلویی چگونه به نظر می‌رسد."], "Product Count": ["تعداد محصول"], "Cart Icon": ["نمادک سبد خرید"], "Icon": ["نمادک"], "Behavior": ["رفتار"], "Display total price": ["نمایش قیمت کل"], "Toggle to display the total price of products in the shopping cart. If no products have been added, the price will not display.": ["تغییر حالت نمایش قیمت کل محصولات در سبد خرید. اگر محصولی اضافه نشده باشد، قیمت نمایش داده نمی‌شود."], "When opened, the Mini-Cart drawer gives shoppers quick access to view their selected products and checkout.": ["هنگامی که باز می شود، کشوی Mini-Cart به خریداران امکان دسترسی سریع برای مشاهده محصولات انتخابی و پرداخت را می دهد."], "Toggle to open the Mini-Cart drawer when a shopper adds a product to their cart.": ["وقتی خریدار محصولی را به سبد خرید خود اضافه می‌کند، آن را تغییر دهید تا کشوی Mini-Cart باز شود."], "Open drawer when adding": ["هنگام افزودن کشو را باز کنید"], "Cart Drawer": ["کشوی سبد خرید"], "Edit Mini-Cart Drawer template": ["قالب کشویی سبد خرید کوچک را ویرایش کنید"], "Mini-Cart in cart and checkout pages": ["سبد خرید کوچک در صفحات سبد خرید و تسویه حساب"], "Select how the Mini-Cart behaves in the Cart and Checkout pages. This might affect the header layout.": ["نحوه رفتار سبد خرید کوچک در صفحات سبد خرید و تسویه حساب را انتخاب کنید. این ممکن است روی طرح سربرگ تاثیر بگذارد."], "Hide": ["م<PERSON><PERSON><PERSON>"], "Display": ["نمایش"], "Never": ["هر<PERSON><PERSON>"], "Price": ["قیمت"], "Remove": ["<PERSON><PERSON><PERSON>"], "Settings": ["پیکربندی"]}}, "comment": {"reference": "assets/client/blocks/mini-cart.js"}}