{"translation-revision-date": "2025-04-09 18:06:38+0000", "generator": "GlotPress/4.0.1", "domain": "messages", "locale_data": {"messages": {"": {"domain": "messages", "plural-forms": "nplurals=1; plural=0;", "lang": "fa"}, "Cart Shortcode": ["کد کوتاه سبد خرید"], "Classic Cart": ["س<PERSON><PERSON> خ<PERSON><PERSON>د کلاسیک"], "Classic Checkout": ["تسویه حساب کلاسیک"], "This block will render the classic cart shortcode. You can optionally transform it into blocks for more control over the cart experience.": ["این بلوک کد کوتاه سبد خرید کلاسیک را ارائه می کند. برای کنترل بیشتر روی تجربه سبد خرید، می‌توانید به صورت اختیاری آن را به بلوک تبدیل کنید."], "This block will render the classic checkout shortcode. You can optionally transform it into blocks for more control over the checkout experience.": ["این بلوک کد کوتاه پرداخت کلاسیک را ارائه می کند. برای کنترل بیشتر بر تجربه پرداخت، می توانید به صورت اختیاری آن را به بلوک تبدیل کنید."], "Renders the classic checkout shortcode.": ["کد کوتاه پرداخت کلاسیک را ارائه می دهد."], "Checkout Cart": ["<PERSON><PERSON><PERSON> خرید"], "Classic shortcode transformed to blocks.": ["کد کوتاه کلاسیک به بلوک تبدیل شده است."], "Renders the classic cart shortcode.": ["کد کوتاه سبد خرید کلاسیک را ارائه می دهد."], "Classic Shortcode Placeholder": ["نگهدارنده مکان کد کوتاه کلاسیک"], "You can learn more about the benefits of switching to blocks, compatibility with extensions, and how to switch back to shortcodes <a>in our documentation</a>.": ["می‌توانید درباره مزایای تغییر به بلوک‌ها، سازگاری با افزودنی‌ها، و نحوه بازگشت به کدهای کوتاه <a>در اسناد ما</a> اطلاعات بیشتری کسب کنید."], "Transform into blocks": ["تبدیل به بلوک"], "Undo": ["بازگردانی"], "Learn more": ["بیشتر بدانید"], "WooCommerce": ["ووکامرس"]}}, "comment": {"reference": "assets/client/blocks/classic-shortcode.js"}}