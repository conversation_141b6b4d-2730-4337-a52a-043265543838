{"translation-revision-date": "2025-04-09 18:06:38+0000", "generator": "GlotPress/4.0.1", "domain": "messages", "locale_data": {"messages": {"": {"domain": "messages", "plural-forms": "nplurals=1; plural=0;", "lang": "fa"}, "Max. Price": ["حدا<PERSON><PERSON><PERSON> قیمت"], "Min. Price": ["ح<PERSON><PERSON><PERSON><PERSON> قیمت"], "Upgrade block": ["ارتقای بلوک"], "Filter block: We have improved this block to make styling easier. Upgrade it using the button below.": ["بلوک فیلتر: ما این بلوک را بهبود بخشیده‌ایم تا استایل‌سازی را آسان‌تر کنیم. با استفاده از دکمه زیر آن را ارتقا دهید."], "To filter your products by price you first need to assign prices to your products.": ["برای فیلتر کردن محصولات خود بر اساس قیمت، ابتدا باید قیمت محصولات خود را تعیین کنید."], "Price Range Selector": ["انتخابگر محدوده قیمت"], "Show 'Apply filters' button": ["نمایش دکمه 'اعمال فیلترها'"], "Inline input fields": ["زمینه‌های ورودی توکار"], "Reset price filter": ["تنظیم دوباره صافی قیمت"], "Reset filter": ["تنظیم دوباره صافی"], "Show input fields inline with the slider.": ["فیلدهای ورودی را با اسلایدر نشان دهید."], "Filter by Price": ["فیلتر بر اساس قیمت"], "Block title": ["عنوان بلوک"], "Apply filter": ["اعمال فیلتر"], "Apply price filter": ["اعمال فیلتر قیمت"], "Editable": ["قابل ویرایش"], "Filter products by maximum price": ["صافی قیمت محصولات با بیشترین قیمت"], "Filter products by minimum price": ["صافی قیمت محصولات با کمترین قیمت"], "Products will update when the button is clicked.": ["محصولات هنگامی که دکمه کلیک شود به‌روز خواهند شد."], "Display a slider to filter products in your store by price.": ["نمایش یک اسلایدر جهت فیلتر کردن محصولات بر اساس قیمت در فروشگاه شما."], "Text": ["متن"], "Filter by price": ["فیلتر بر اساس قیمت"], "Add new product": ["افزودن محصول جدید"], "Reset": ["بازگردانی"], "Learn more": ["بیشتر بدانید"], "Apply": ["اعمال کردن"], "Settings": ["پیکربندی"]}}, "comment": {"reference": "assets/client/blocks/price-filter.js"}}